#!/usr/bin/env python3
"""
GAAPF CLI Entry Point

This script provides the main entry point for running the GAAPF CLI application.
It handles environment setup and launches the interactive learning interface.
"""

import sys
import os
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# Import and run the CLI application
from pyframeworks_assistant.interfaces.cli.cli_app import main
import asyncio

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye! Thanks for using GAAPF!")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Error starting GAAPF: {e}")
        print("Please check your environment configuration and try again.")
        sys.exit(1)
