"""
Knowledge domain agents for the GAAPF constellation system.

This module implements agents specialized in knowledge delivery:
- InstructorAgent: Provides structured explanations and learning guidance
- DocumentationExpertAgent: Offers detailed documentation and references
- ResearchAssistantAgent: Explores advanced topics and cutting-edge features
- KnowledgeSynthesizerAgent: Synthesizes information from multiple sources
"""

from typing import Dict, Any, List, Optional
from datetime import datetime

from .base_agent import BaseAgent, AgentResponse, HandoffSuggestion, HandoffConfidence
from ..config.user_profiles import UserProfile
from ..config.framework_configs import SupportedFrameworks
from ..config.constellation_configs import AgentRole


class InstructorAgent(BaseAgent):
    """
    Primary teaching agent that provides structured explanations and learning guidance.
    
    Specializes in:
    - Clear, structured explanations
    - Learning path guidance
    - Concept introduction and reinforcement
    - Pedagogical content delivery
    """
    
    def __init__(self, llm_client: Any, **kwargs):
        system_prompt = """You are an expert AI instructor specializing in Python AI frameworks. 
Your role is to provide clear, structured explanations and guide learners through their learning journey.

Key responsibilities:
- Provide clear, well-structured explanations of concepts
- Break down complex topics into digestible parts
- Adapt explanations to the user's skill level
- Suggest logical learning progressions
- Encourage questions and deeper understanding

Communication style:
- Use clear, accessible language
- Provide examples when helpful
- Structure responses with headings and bullet points
- Be encouraging and supportive
- Ask clarifying questions when needed"""

        super().__init__(
            agent_role=AgentRole.INSTRUCTOR,
            llm_client=llm_client,
            system_prompt=system_prompt,
            handoff_keywords=[
                "code", "example", "implementation", "practice", "exercise",
                "documentation", "docs", "reference", "research", "advanced"
            ],
            **kwargs
        )
    
    async def generate_response(
        self,
        user_message: str,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
        session_context: Dict[str, Any],
    ) -> AgentResponse:
        """Generate instructional response."""
        start_time = datetime.utcnow()
        
        # Build context-aware system prompt
        system_prompt = self._build_system_prompt(user_profile, framework, module_id)
        
        # Prepare messages for LLM
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_message}
        ]
        
        # Add recent history for context
        recent_history = self.get_recent_history(3)
        for msg in recent_history:
            messages.insert(-1, {"role": msg["role"], "content": msg["content"]})
        
        # Generate response
        content = await self._call_llm(messages)
        
        # Add to history
        self.add_to_history("user", user_message)
        self.add_to_history("assistant", content)
        
        # Analyze for handoffs
        handoff_suggestion = self.analyze_handoff_opportunity(user_message, session_context)
        
        # Calculate processing time
        processing_time = (datetime.utcnow() - start_time).total_seconds()
        
        return AgentResponse(
            agent_role=self.agent_role,
            content=content,
            response_type="instructional",
            handoff_suggestion=handoff_suggestion,
            learning_objectives_addressed=self._extract_learning_objectives(content),
            difficulty_level=self._assess_difficulty_level(user_profile, content),
            estimated_reading_time_minutes=len(content.split()) / 200,  # ~200 words per minute
            includes_code_example="```" in content,
            includes_exercise="exercise" in content.lower() or "try" in content.lower(),
            includes_links="http" in content or "docs" in content.lower(),
            processing_time_seconds=processing_time,
        )
    
    def analyze_handoff_opportunity(
        self,
        user_message: str,
        current_context: Dict[str, Any],
    ) -> Optional[HandoffSuggestion]:
        """Analyze if handoff would benefit the user."""
        message_lower = user_message.lower()
        
        # Check for code-related requests
        if any(keyword in message_lower for keyword in ["code", "example", "implement", "show me how"]):
            return HandoffSuggestion(
                target_agent=AgentRole.CODE_ASSISTANT,
                confidence=HandoffConfidence.HIGH,
                reason="User is requesting code examples or implementation guidance",
                context_data={"topic": user_message, "explanation_provided": True}
            )
        
        # Check for practice requests
        if any(keyword in message_lower for keyword in ["practice", "exercise", "hands-on", "try"]):
            return HandoffSuggestion(
                target_agent=AgentRole.PRACTICE_FACILITATOR,
                confidence=HandoffConfidence.HIGH,
                reason="User wants to practice or do exercises",
                context_data={"concept_explained": True, "ready_for_practice": True}
            )
        
        # Check for documentation requests
        if any(keyword in message_lower for keyword in ["docs", "documentation", "reference", "api"]):
            return HandoffSuggestion(
                target_agent=AgentRole.DOCUMENTATION_EXPERT,
                confidence=HandoffConfidence.MEDIUM,
                reason="User needs detailed documentation or reference information",
                context_data={"concept_introduced": True}
            )
        
        return None
    
    def _extract_learning_objectives(self, content: str) -> List[str]:
        """Extract learning objectives from response content."""
        # Simple keyword-based extraction
        objectives = []
        content_lower = content.lower()
        
        if "understand" in content_lower:
            objectives.append("conceptual_understanding")
        if "learn" in content_lower:
            objectives.append("knowledge_acquisition")
        if "apply" in content_lower:
            objectives.append("practical_application")
        
        return objectives
    
    def _assess_difficulty_level(self, user_profile: UserProfile, content: str) -> str:
        """Assess the difficulty level of the content."""
        if user_profile.python_skill_level.value in ["none", "beginner"]:
            return "beginner"
        elif user_profile.python_skill_level.value == "intermediate":
            return "intermediate"
        else:
            return "advanced"


class DocumentationExpertAgent(BaseAgent):
    """
    Agent specialized in providing detailed documentation and reference information.
    
    Specializes in:
    - Official documentation references
    - API documentation
    - Parameter explanations
    - Method and class references
    """
    
    def __init__(self, llm_client: Any, **kwargs):
        system_prompt = """You are a documentation expert for Python AI frameworks.
Your role is to provide detailed, accurate documentation and reference information.

Key responsibilities:
- Provide comprehensive API documentation
- Explain parameters, methods, and classes in detail
- Reference official documentation sources
- Clarify usage patterns and best practices
- Maintain accuracy and completeness

Communication style:
- Be precise and detailed
- Use proper technical terminology
- Include parameter types and descriptions
- Provide links to official documentation
- Structure information clearly with examples"""

        super().__init__(
            agent_role=AgentRole.DOCUMENTATION_EXPERT,
            llm_client=llm_client,
            system_prompt=system_prompt,
            handoff_keywords=[
                "example", "implement", "practice", "project", "build"
            ],
            **kwargs
        )
    
    async def generate_response(
        self,
        user_message: str,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
        session_context: Dict[str, Any],
    ) -> AgentResponse:
        """Generate documentation-focused response."""
        start_time = datetime.utcnow()
        
        system_prompt = self._build_system_prompt(user_profile, framework, module_id)
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_message}
        ]
        
        content = await self._call_llm(messages)
        
        self.add_to_history("user", user_message)
        self.add_to_history("assistant", content)
        
        handoff_suggestion = self.analyze_handoff_opportunity(user_message, session_context)
        processing_time = (datetime.utcnow() - start_time).total_seconds()
        
        return AgentResponse(
            agent_role=self.agent_role,
            content=content,
            response_type="documentation",
            handoff_suggestion=handoff_suggestion,
            learning_objectives_addressed=["reference_knowledge", "api_understanding"],
            difficulty_level="intermediate",
            estimated_reading_time_minutes=len(content.split()) / 180,  # Slower for technical content
            includes_code_example="```" in content,
            includes_links=True,  # Documentation responses typically include links
            processing_time_seconds=processing_time,
        )
    
    def analyze_handoff_opportunity(
        self,
        user_message: str,
        current_context: Dict[str, Any],
    ) -> Optional[HandoffSuggestion]:
        """Analyze handoff opportunities from documentation context."""
        message_lower = user_message.lower()
        
        if any(keyword in message_lower for keyword in ["example", "show", "implement"]):
            return HandoffSuggestion(
                target_agent=AgentRole.CODE_ASSISTANT,
                confidence=HandoffConfidence.HIGH,
                reason="User wants to see practical implementation after documentation",
                context_data={"documentation_provided": True}
            )
        
        return None


class ResearchAssistantAgent(BaseAgent):
    """
    Agent specialized in exploring advanced topics and cutting-edge features.
    
    Specializes in:
    - Advanced feature exploration
    - Latest updates and changes
    - Experimental features
    - Research-oriented content
    """
    
    def __init__(self, llm_client: Any, **kwargs):
        system_prompt = """You are a research assistant specializing in advanced Python AI framework topics.
Your role is to explore cutting-edge features, latest developments, and advanced use cases.

Key responsibilities:
- Research and explain advanced features
- Discuss latest framework updates
- Explore experimental capabilities
- Provide insights into best practices
- Connect concepts across different frameworks

Communication style:
- Be thorough and analytical
- Discuss trade-offs and considerations
- Reference recent developments
- Encourage exploration and experimentation
- Provide context for advanced concepts"""

        super().__init__(
            agent_role=AgentRole.RESEARCH_ASSISTANT,
            llm_client=llm_client,
            system_prompt=system_prompt,
            handoff_keywords=[
                "synthesize", "summary", "overview", "explain simply"
            ],
            **kwargs
        )
    
    async def generate_response(
        self,
        user_message: str,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
        session_context: Dict[str, Any],
    ) -> AgentResponse:
        """Generate research-focused response."""
        start_time = datetime.utcnow()
        
        system_prompt = self._build_system_prompt(user_profile, framework, module_id)
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_message}
        ]
        
        content = await self._call_llm(messages)
        
        self.add_to_history("user", user_message)
        self.add_to_history("assistant", content)
        
        handoff_suggestion = self.analyze_handoff_opportunity(user_message, session_context)
        processing_time = (datetime.utcnow() - start_time).total_seconds()
        
        return AgentResponse(
            agent_role=self.agent_role,
            content=content,
            response_type="research",
            handoff_suggestion=handoff_suggestion,
            learning_objectives_addressed=["advanced_understanding", "research_skills"],
            difficulty_level="advanced",
            estimated_reading_time_minutes=len(content.split()) / 160,  # Slower for complex content
            includes_links=True,
            processing_time_seconds=processing_time,
        )
    
    def analyze_handoff_opportunity(
        self,
        user_message: str,
        current_context: Dict[str, Any],
    ) -> Optional[HandoffSuggestion]:
        """Analyze handoff opportunities from research context."""
        message_lower = user_message.lower()
        
        if any(keyword in message_lower for keyword in ["synthesize", "summary", "overview"]):
            return HandoffSuggestion(
                target_agent=AgentRole.KNOWLEDGE_SYNTHESIZER,
                confidence=HandoffConfidence.MEDIUM,
                reason="User needs synthesis of research information",
                context_data={"research_completed": True}
            )
        
        return None


class KnowledgeSynthesizerAgent(BaseAgent):
    """
    Agent specialized in synthesizing information from multiple sources.
    
    Specializes in:
    - Information synthesis
    - Concept integration
    - Cross-framework comparisons
    - Knowledge consolidation
    """
    
    def __init__(self, llm_client: Any, **kwargs):
        system_prompt = """You are a knowledge synthesizer for Python AI frameworks.
Your role is to integrate and synthesize information from multiple sources and perspectives.

Key responsibilities:
- Synthesize complex information into coherent understanding
- Connect concepts across different frameworks
- Provide integrated perspectives
- Consolidate learning from multiple sources
- Create comprehensive overviews

Communication style:
- Be integrative and holistic
- Show connections between concepts
- Provide structured summaries
- Use clear organization and flow
- Help users see the bigger picture"""

        super().__init__(
            agent_role=AgentRole.KNOWLEDGE_SYNTHESIZER,
            llm_client=llm_client,
            system_prompt=system_prompt,
            handoff_keywords=[
                "practice", "apply", "implement", "project"
            ],
            **kwargs
        )
    
    async def generate_response(
        self,
        user_message: str,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
        session_context: Dict[str, Any],
    ) -> AgentResponse:
        """Generate synthesis-focused response."""
        start_time = datetime.utcnow()
        
        system_prompt = self._build_system_prompt(user_profile, framework, module_id)
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_message}
        ]
        
        content = await self._call_llm(messages)
        
        self.add_to_history("user", user_message)
        self.add_to_history("assistant", content)
        
        handoff_suggestion = self.analyze_handoff_opportunity(user_message, session_context)
        processing_time = (datetime.utcnow() - start_time).total_seconds()
        
        return AgentResponse(
            agent_role=self.agent_role,
            content=content,
            response_type="synthesis",
            handoff_suggestion=handoff_suggestion,
            learning_objectives_addressed=["knowledge_integration", "conceptual_synthesis"],
            difficulty_level="intermediate",
            estimated_reading_time_minutes=len(content.split()) / 190,
            processing_time_seconds=processing_time,
        )
    
    def analyze_handoff_opportunity(
        self,
        user_message: str,
        current_context: Dict[str, Any],
    ) -> Optional[HandoffSuggestion]:
        """Analyze handoff opportunities from synthesis context."""
        message_lower = user_message.lower()
        
        if any(keyword in message_lower for keyword in ["practice", "apply", "implement"]):
            return HandoffSuggestion(
                target_agent=AgentRole.PRACTICE_FACILITATOR,
                confidence=HandoffConfidence.MEDIUM,
                reason="User ready to apply synthesized knowledge",
                context_data={"synthesis_completed": True}
            )
        
        return None
