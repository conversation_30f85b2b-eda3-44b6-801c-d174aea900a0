"""
Adaptive engine for intelligent learning optimization.

This module implements the adaptive learning engine that continuously
optimizes the learning experience based on user behavior, performance,
and temporal patterns.
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from pydantic import BaseModel, Field
from enum import Enum

from ..config.user_profiles import UserProfile
from ..config.framework_configs import SupportedFrameworks
from ..config.constellation_configs import ConstellationType, get_optimal_constellation_for_profile
from .temporal_state import TemporalStateManager
from .constellation import ConstellationManager


class AdaptationTrigger(str, Enum):
    """Triggers for adaptive adjustments."""
    LOW_ENGAGEMENT = "low_engagement"
    POOR_COMPREHENSION = "poor_comprehension"
    RAPID_PROGRESS = "rapid_progress"
    LEARNING_PLATEAU = "learning_plateau"
    PREFERENCE_SHIFT = "preference_shift"
    TEMPORAL_PATTERN = "temporal_pattern"


class AdaptationRecommendation(BaseModel):
    """Recommendation for adaptive adjustment."""
    
    trigger: AdaptationTrigger = Field(..., description="What triggered this recommendation")
    recommendation_type: str = Field(..., description="Type of recommendation")
    description: str = Field(..., description="Human-readable description")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence in recommendation")
    
    # Specific recommendations
    suggested_constellation: Optional[ConstellationType] = Field(None, description="Suggested constellation change")
    suggested_pace_adjustment: Optional[float] = Field(None, description="Suggested pace multiplier")
    suggested_difficulty_adjustment: Optional[float] = Field(None, description="Suggested difficulty multiplier")
    suggested_session_length: Optional[int] = Field(None, description="Suggested session length in minutes")
    
    # Implementation details
    implementation_priority: str = Field("medium", description="Priority: low, medium, high, critical")
    expected_impact: str = Field("moderate", description="Expected impact: low, moderate, high")
    
    class Config:
        use_enum_values = True


class LearningContext(BaseModel):
    """Current learning context for adaptation decisions."""
    
    user_id: str = Field(..., description="User identifier")
    current_session_id: Optional[str] = Field(None, description="Current session ID")
    
    # Current state
    current_framework: SupportedFrameworks = Field(..., description="Current framework")
    current_module: str = Field(..., description="Current module")
    current_constellation: ConstellationType = Field(..., description="Current constellation")
    
    # Recent performance
    recent_effectiveness_scores: List[float] = Field(default_factory=list, description="Recent effectiveness scores")
    recent_engagement_scores: List[float] = Field(default_factory=list, description="Recent engagement scores")
    recent_session_durations: List[float] = Field(default_factory=list, description="Recent session durations")
    
    # Behavioral patterns
    interaction_velocity: float = Field(0.0, description="Interactions per minute")
    learning_momentum: str = Field("stable", description="Learning momentum: declining, stable, improving")
    consistency_score: float = Field(0.5, description="Learning consistency score")
    
    # Temporal data
    last_adaptation: Optional[datetime] = Field(None, description="Last adaptation timestamp")
    adaptation_history: List[str] = Field(default_factory=list, description="Recent adaptations")
    
    class Config:
        use_enum_values = True


class AdaptiveEngine:
    """
    Intelligent adaptive engine for learning optimization.
    
    This engine continuously monitors learning patterns and automatically
    suggests or implements adaptations to optimize the learning experience.
    """
    
    def __init__(
        self,
        temporal_manager: TemporalStateManager,
        constellation_manager: ConstellationManager,
        auto_adapt: bool = False,
    ):
        """Initialize adaptive engine."""
        self.temporal_manager = temporal_manager
        self.constellation_manager = constellation_manager
        self.auto_adapt = auto_adapt
        
        # Adaptation state
        self.learning_contexts: Dict[str, LearningContext] = {}
        self.adaptation_thresholds = {
            "low_engagement": 0.4,
            "poor_comprehension": 0.5,
            "rapid_progress": 0.9,
            "consistency_threshold": 0.3,
        }
    
    async def analyze_learning_context(
        self,
        user_profile: UserProfile,
        session_data: Dict[str, Any],
    ) -> LearningContext:
        """Analyze current learning context for adaptation opportunities."""
        
        user_id = user_profile.user_id
        
        # Get or create learning context
        if user_id not in self.learning_contexts:
            self.learning_contexts[user_id] = LearningContext(
                user_id=user_id,
                current_framework=SupportedFrameworks.LANGCHAIN,  # Default
                current_module="unknown",
                current_constellation=ConstellationType.THEORY_PRACTICE_BALANCED,
            )
        
        context = self.learning_contexts[user_id]
        
        # Update context with session data
        if session_data:
            context.current_session_id = session_data.get("session_id")
            context.current_framework = SupportedFrameworks(session_data.get("framework", "langchain"))
            context.current_module = session_data.get("module_id", "unknown")
            context.current_constellation = ConstellationType(session_data.get("constellation_type", "theory_practice_balanced"))
            
            # Update performance metrics
            if "effectiveness_score" in session_data:
                context.recent_effectiveness_scores.append(session_data["effectiveness_score"])
                if len(context.recent_effectiveness_scores) > 10:
                    context.recent_effectiveness_scores.pop(0)
            
            if "engagement_score" in session_data:
                context.recent_engagement_scores.append(session_data["engagement_score"])
                if len(context.recent_engagement_scores) > 10:
                    context.recent_engagement_scores.pop(0)
            
            if "duration_minutes" in session_data:
                context.recent_session_durations.append(session_data["duration_minutes"])
                if len(context.recent_session_durations) > 10:
                    context.recent_session_durations.pop(0)
            
            # Calculate behavioral patterns
            context.interaction_velocity = session_data.get("interaction_rate", 0.0)
            context.learning_momentum = self._calculate_learning_momentum(context)
            context.consistency_score = self._calculate_consistency_score(context)
        
        return context
    
    async def generate_adaptation_recommendations(
        self,
        user_profile: UserProfile,
        learning_context: LearningContext,
    ) -> List[AdaptationRecommendation]:
        """Generate adaptation recommendations based on learning context."""
        
        recommendations = []
        
        # Check for low engagement
        if learning_context.recent_engagement_scores:
            avg_engagement = sum(learning_context.recent_engagement_scores) / len(learning_context.recent_engagement_scores)
            if avg_engagement < self.adaptation_thresholds["low_engagement"]:
                recommendations.append(await self._recommend_engagement_boost(learning_context))
        
        # Check for poor comprehension
        if learning_context.recent_effectiveness_scores:
            avg_effectiveness = sum(learning_context.recent_effectiveness_scores) / len(learning_context.recent_effectiveness_scores)
            if avg_effectiveness < self.adaptation_thresholds["poor_comprehension"]:
                recommendations.append(await self._recommend_comprehension_support(learning_context))
        
        # Check for rapid progress
        if learning_context.recent_effectiveness_scores:
            recent_avg = sum(learning_context.recent_effectiveness_scores[-3:]) / min(3, len(learning_context.recent_effectiveness_scores))
            if recent_avg > self.adaptation_thresholds["rapid_progress"]:
                recommendations.append(await self._recommend_acceleration(learning_context))
        
        # Check for learning plateau
        if len(learning_context.recent_effectiveness_scores) >= 5:
            if self._detect_plateau(learning_context.recent_effectiveness_scores):
                recommendations.append(await self._recommend_plateau_break(learning_context))
        
        # Check for consistency issues
        if learning_context.consistency_score < self.adaptation_thresholds["consistency_threshold"]:
            recommendations.append(await self._recommend_consistency_improvement(learning_context))
        
        # Check for temporal patterns
        temporal_recommendation = await self._analyze_temporal_patterns(user_profile, learning_context)
        if temporal_recommendation:
            recommendations.append(temporal_recommendation)
        
        # Sort by priority and confidence
        recommendations.sort(key=lambda r: (
            {"critical": 4, "high": 3, "medium": 2, "low": 1}[r.implementation_priority],
            r.confidence
        ), reverse=True)
        
        return recommendations
    
    async def _recommend_engagement_boost(self, context: LearningContext) -> AdaptationRecommendation:
        """Recommend changes to boost engagement."""
        
        # Suggest more interactive constellation
        if context.current_constellation not in [ConstellationType.HANDS_ON_FOCUSED, ConstellationType.PROJECT_ORIENTED]:
            suggested_constellation = ConstellationType.HANDS_ON_FOCUSED
        else:
            suggested_constellation = ConstellationType.PROJECT_ORIENTED
        
        return AdaptationRecommendation(
            trigger=AdaptationTrigger.LOW_ENGAGEMENT,
            recommendation_type="constellation_change",
            description=f"Switch to {suggested_constellation.value.replace('_', ' ').title()} constellation to increase engagement through more interactive learning",
            confidence=0.8,
            suggested_constellation=suggested_constellation,
            implementation_priority="high",
            expected_impact="high",
        )
    
    async def _recommend_comprehension_support(self, context: LearningContext) -> AdaptationRecommendation:
        """Recommend changes to improve comprehension."""
        
        return AdaptationRecommendation(
            trigger=AdaptationTrigger.POOR_COMPREHENSION,
            recommendation_type="pace_adjustment",
            description="Slow down learning pace and switch to Knowledge Intensive constellation for better conceptual understanding",
            confidence=0.75,
            suggested_constellation=ConstellationType.KNOWLEDGE_INTENSIVE,
            suggested_pace_adjustment=0.7,
            implementation_priority="high",
            expected_impact="moderate",
        )
    
    async def _recommend_acceleration(self, context: LearningContext) -> AdaptationRecommendation:
        """Recommend acceleration for rapid learners."""
        
        return AdaptationRecommendation(
            trigger=AdaptationTrigger.RAPID_PROGRESS,
            recommendation_type="acceleration",
            description="Increase learning pace and introduce more challenging content",
            confidence=0.85,
            suggested_pace_adjustment=1.3,
            suggested_difficulty_adjustment=1.2,
            implementation_priority="medium",
            expected_impact="moderate",
        )
    
    async def _recommend_plateau_break(self, context: LearningContext) -> AdaptationRecommendation:
        """Recommend changes to break learning plateau."""
        
        return AdaptationRecommendation(
            trigger=AdaptationTrigger.LEARNING_PLATEAU,
            recommendation_type="approach_change",
            description="Change learning approach to break plateau - try Research Intensive constellation for deeper exploration",
            confidence=0.7,
            suggested_constellation=ConstellationType.RESEARCH_INTENSIVE,
            implementation_priority="medium",
            expected_impact="moderate",
        )
    
    async def _recommend_consistency_improvement(self, context: LearningContext) -> AdaptationRecommendation:
        """Recommend changes to improve learning consistency."""
        
        # Suggest shorter, more frequent sessions
        avg_duration = sum(context.recent_session_durations) / max(1, len(context.recent_session_durations))
        suggested_duration = max(15, int(avg_duration * 0.7))
        
        return AdaptationRecommendation(
            trigger=AdaptationTrigger.PREFERENCE_SHIFT,
            recommendation_type="session_optimization",
            description=f"Try shorter sessions ({suggested_duration} minutes) with Assessment Focused constellation to improve consistency",
            confidence=0.6,
            suggested_constellation=ConstellationType.ASSESSMENT_FOCUSED,
            suggested_session_length=suggested_duration,
            implementation_priority="low",
            expected_impact="moderate",
        )
    
    async def _analyze_temporal_patterns(
        self,
        user_profile: UserProfile,
        context: LearningContext,
    ) -> Optional[AdaptationRecommendation]:
        """Analyze temporal patterns for optimization opportunities."""
        
        # Get user analytics from temporal manager
        analytics = self.temporal_manager.get_user_learning_analytics(user_profile.user_id)
        
        if analytics.get("message"):  # No history available
            return None
        
        # Check if current constellation is suboptimal
        preferred_constellation = analytics.get("preferred_constellation")
        if preferred_constellation and preferred_constellation != context.current_constellation.value:
            try:
                suggested_constellation = ConstellationType(preferred_constellation)
                return AdaptationRecommendation(
                    trigger=AdaptationTrigger.TEMPORAL_PATTERN,
                    recommendation_type="historical_optimization",
                    description=f"Based on your learning history, {suggested_constellation.value.replace('_', ' ').title()} constellation works best for you",
                    confidence=0.9,
                    suggested_constellation=suggested_constellation,
                    implementation_priority="medium",
                    expected_impact="high",
                )
            except ValueError:
                pass  # Invalid constellation type
        
        return None
    
    def _calculate_learning_momentum(self, context: LearningContext) -> str:
        """Calculate learning momentum trend."""
        
        if len(context.recent_effectiveness_scores) < 3:
            return "stable"
        
        recent_scores = context.recent_effectiveness_scores[-3:]
        if len(recent_scores) < 3:
            return "stable"
        
        # Simple trend analysis
        if recent_scores[-1] > recent_scores[0] + 0.1:
            return "improving"
        elif recent_scores[-1] < recent_scores[0] - 0.1:
            return "declining"
        else:
            return "stable"
    
    def _calculate_consistency_score(self, context: LearningContext) -> float:
        """Calculate learning consistency score."""
        
        if len(context.recent_session_durations) < 3:
            return 0.5  # Default neutral score
        
        durations = context.recent_session_durations
        
        # Calculate coefficient of variation (lower is more consistent)
        mean_duration = sum(durations) / len(durations)
        if mean_duration == 0:
            return 0.0
        
        variance = sum((d - mean_duration) ** 2 for d in durations) / len(durations)
        std_dev = variance ** 0.5
        cv = std_dev / mean_duration
        
        # Convert to consistency score (0-1, higher is more consistent)
        consistency = max(0.0, 1.0 - cv)
        return min(1.0, consistency)
    
    def _detect_plateau(self, scores: List[float]) -> bool:
        """Detect if learning has plateaued."""
        
        if len(scores) < 5:
            return False
        
        # Check if recent scores are not improving
        recent_scores = scores[-5:]
        
        # Calculate trend
        x = list(range(len(recent_scores)))
        y = recent_scores
        
        # Simple linear regression slope
        n = len(x)
        slope = (n * sum(x[i] * y[i] for i in range(n)) - sum(x) * sum(y)) / (n * sum(x[i]**2 for i in range(n)) - sum(x)**2)
        
        # Plateau if slope is very small (not improving)
        return abs(slope) < 0.02
    
    async def apply_adaptation(
        self,
        user_profile: UserProfile,
        recommendation: AdaptationRecommendation,
        session_id: Optional[str] = None,
    ) -> bool:
        """Apply an adaptation recommendation."""
        
        try:
            # Update learning context
            context = self.learning_contexts.get(user_profile.user_id)
            if context:
                context.last_adaptation = datetime.utcnow()
                context.adaptation_history.append(recommendation.recommendation_type)
                if len(context.adaptation_history) > 10:
                    context.adaptation_history.pop(0)
            
            # Apply constellation change if suggested
            if recommendation.suggested_constellation and session_id:
                # This would require constellation manager to support runtime changes
                # For now, we'll record the recommendation for next session
                pass
            
            # Record successful adaptation
            return True
            
        except Exception as e:
            # Log error and return failure
            print(f"Failed to apply adaptation: {e}")
            return False
    
    def get_adaptation_summary(self, user_id: str) -> Dict[str, Any]:
        """Get summary of adaptations for a user."""
        
        context = self.learning_contexts.get(user_id)
        if not context:
            return {"message": "No adaptation history available"}
        
        return {
            "current_constellation": context.current_constellation,
            "learning_momentum": context.learning_momentum,
            "consistency_score": round(context.consistency_score, 3),
            "recent_adaptations": context.adaptation_history[-5:],
            "last_adaptation": context.last_adaptation.isoformat() if context.last_adaptation else None,
            "performance_trend": {
                "effectiveness": context.recent_effectiveness_scores[-5:] if context.recent_effectiveness_scores else [],
                "engagement": context.recent_engagement_scores[-5:] if context.recent_engagement_scores else [],
            },
        }
