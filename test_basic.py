#!/usr/bin/env python3
"""
Basic functionality test for GAAPF system.

This script tests core functionality to ensure the system is working correctly.
"""

import sys
import asyncio
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from pyframeworks_assistant.config.user_profiles import (
    UserProfile, SkillLevel, LearningPace, LearningStyle
)
from pyframeworks_assistant.config.framework_configs import (
    SupportedFrameworks, get_framework_config, get_available_frameworks
)
from pyframeworks_assistant.config.constellation_configs import (
    ConstellationType, get_constellation_config, get_available_constellations
)
from pyframeworks_assistant.core.constellation import ConstellationManager
from pyframeworks_assistant.core.temporal_state import TemporalStateManager
from pyframeworks_assistant.core.learning_hub import LearningHub
from pyframeworks_assistant.core.adaptive_engine import AdaptiveE<PERSON>ine


def test_user_profile():
    """Test user profile creation and validation."""
    print("🧪 Testing User Profile...")
    
    profile = UserProfile(
        user_id="test_user",
        name="Test User",
        programming_experience_years=3,
        python_skill_level=SkillLevel.INTERMEDIATE,
        learning_pace=LearningPace.MODERATE,
        preferred_learning_style=LearningStyle.HANDS_ON,
        learning_goals=["Learn LangChain", "Build RAG applications"]
    )
    
    assert profile.user_id == "test_user"
    assert profile.python_skill_level == SkillLevel.INTERMEDIATE
    assert profile.get_experience_level() == "Intermediate"
    assert profile.is_suitable_for_advanced_topics() == False
    
    print("✅ User Profile tests passed!")
    return profile


def test_framework_configs():
    """Test framework configuration system."""
    print("🧪 Testing Framework Configurations...")
    
    # Test available frameworks
    frameworks = get_available_frameworks()
    assert len(frameworks) >= 2  # At least LangChain and LangGraph
    assert SupportedFrameworks.LANGCHAIN in frameworks
    assert SupportedFrameworks.LANGGRAPH in frameworks
    
    # Test LangChain config
    langchain_config = get_framework_config(SupportedFrameworks.LANGCHAIN)
    assert langchain_config.display_name == "LangChain"
    assert len(langchain_config.modules) >= 3
    assert langchain_config.is_fully_supported == True
    
    # Test LangGraph config
    langgraph_config = get_framework_config(SupportedFrameworks.LANGGRAPH)
    assert langgraph_config.display_name == "LangGraph"
    assert len(langgraph_config.modules) >= 1
    
    print("✅ Framework Configuration tests passed!")


def test_constellation_configs():
    """Test constellation configuration system."""
    print("🧪 Testing Constellation Configurations...")
    
    # Test available constellations
    constellations = get_available_constellations()
    assert len(constellations) == 8  # All 8 constellation types
    
    # Test specific constellation
    hands_on_config = get_constellation_config(ConstellationType.HANDS_ON_FOCUSED)
    assert hands_on_config.display_name == "Hands-On Focused"
    assert hands_on_config.practical_weight > hands_on_config.theoretical_weight
    assert len(hands_on_config.primary_agents) >= 3
    
    # Test knowledge intensive constellation
    knowledge_config = get_constellation_config(ConstellationType.KNOWLEDGE_INTENSIVE)
    assert knowledge_config.theoretical_weight > knowledge_config.practical_weight
    
    print("✅ Constellation Configuration tests passed!")


async def test_constellation_manager():
    """Test constellation manager functionality."""
    print("🧪 Testing Constellation Manager...")
    
    # Create test profile
    profile = UserProfile(
        user_id="test_user_cm",
        python_skill_level=SkillLevel.INTERMEDIATE,
        preferred_learning_style=LearningStyle.HANDS_ON,
        learning_pace=LearningPace.MODERATE
    )
    
    # Initialize constellation manager
    manager = ConstellationManager(llm_client=None)  # Mock LLM client
    
    # Create constellation
    constellation = await manager.create_constellation(
        ConstellationType.HANDS_ON_FOCUSED,
        profile,
        SupportedFrameworks.LANGCHAIN,
        "lc_basics",
        "test_session_123"
    )
    
    assert constellation.constellation_type == ConstellationType.HANDS_ON_FOCUSED
    assert constellation.user_id == "test_user_cm"
    assert len(constellation.active_agents) >= 3
    assert constellation.get_primary_agent() is not None
    
    # Test session run (would normally interact with LLM)
    try:
        response = await manager.run_session(
            "test_session_123",
            "What is LangChain?",
            profile,
            SupportedFrameworks.LANGCHAIN,
            "lc_basics"
        )
        assert response.agent_role is not None
        assert len(response.content) > 0
    except Exception as e:
        print(f"⚠️ Session run test skipped (expected with mock LLM): {e}")
    
    print("✅ Constellation Manager tests passed!")


async def test_temporal_state_manager():
    """Test temporal state management."""
    print("🧪 Testing Temporal State Manager...")
    
    # Initialize temporal manager
    temporal_manager = TemporalStateManager(storage_path="data/test_temporal")
    
    # Test optimization (should return default values with no history)
    profile = UserProfile(
        user_id="test_user_ts",
        python_skill_level=SkillLevel.INTERMEDIATE,
        preferred_learning_style=LearningStyle.HANDS_ON
    )
    
    optimal_constellation, confidence = await temporal_manager.optimize_constellation_selection(
        profile,
        SupportedFrameworks.LANGCHAIN,
        "lc_basics",
        {}
    )
    
    assert optimal_constellation in get_available_constellations()
    assert 0.0 <= confidence <= 1.0
    
    # Test analytics (should handle empty history gracefully)
    analytics = temporal_manager.get_user_learning_analytics("test_user_ts")
    assert "message" in analytics or "total_sessions" in analytics
    
    print("✅ Temporal State Manager tests passed!")


async def test_learning_hub():
    """Test learning hub integration."""
    print("🧪 Testing Learning Hub...")
    
    # Initialize components
    constellation_manager = ConstellationManager(llm_client=None)
    temporal_manager = TemporalStateManager(storage_path="data/test_temporal")
    learning_hub = LearningHub(constellation_manager, temporal_manager)
    
    # Create test profile
    profile = UserProfile(
        user_id="test_user_lh",
        python_skill_level=SkillLevel.INTERMEDIATE,
        preferred_learning_style=LearningStyle.HANDS_ON
    )
    
    # Start learning session
    session_info = await learning_hub.start_learning_session(
        profile,
        SupportedFrameworks.LANGCHAIN
    )
    
    assert "session_id" in session_info
    assert "constellation_type" in session_info
    assert "module_info" in session_info
    assert "welcome_message" in session_info
    
    session_id = session_info["session_id"]
    
    # Test interaction (would normally process through LLM)
    try:
        interaction_result = await learning_hub.process_learning_interaction(
            session_id,
            "Tell me about LangChain",
            profile
        )
        assert "response" in interaction_result
        assert "session_info" in interaction_result
    except Exception as e:
        print(f"⚠️ Interaction test skipped (expected with mock LLM): {e}")
    
    # End session
    summary = await learning_hub.end_learning_session(session_id)
    assert "session_summary" in summary
    assert "learning_progress" in summary
    
    print("✅ Learning Hub tests passed!")


async def test_adaptive_engine():
    """Test adaptive engine functionality."""
    print("🧪 Testing Adaptive Engine...")
    
    # Initialize components
    constellation_manager = ConstellationManager(llm_client=None)
    temporal_manager = TemporalStateManager(storage_path="data/test_temporal")
    adaptive_engine = AdaptiveEngine(temporal_manager, constellation_manager)
    
    # Create test profile
    profile = UserProfile(
        user_id="test_user_ae",
        python_skill_level=SkillLevel.INTERMEDIATE
    )
    
    # Test context analysis
    session_data = {
        "session_id": "test_session",
        "framework": "langchain",
        "module_id": "lc_basics",
        "constellation_type": "hands_on_focused",
        "effectiveness_score": 0.7,
        "engagement_score": 0.8,
        "duration_minutes": 30.0,
    }
    
    context = await adaptive_engine.analyze_learning_context(profile, session_data)
    assert context.user_id == "test_user_ae"
    assert context.current_framework == SupportedFrameworks.LANGCHAIN
    
    # Test recommendations
    recommendations = await adaptive_engine.generate_adaptation_recommendations(profile, context)
    assert isinstance(recommendations, list)
    
    # Test adaptation summary
    summary = adaptive_engine.get_adaptation_summary("test_user_ae")
    assert isinstance(summary, dict)
    
    print("✅ Adaptive Engine tests passed!")


def test_imports():
    """Test that all imports work correctly."""
    print("🧪 Testing Imports...")
    
    # Test config imports
    from pyframeworks_assistant.config import (
        UserProfile, SkillLevel, SupportedFrameworks, ConstellationType
    )
    
    # Test core imports
    from pyframeworks_assistant.core import (
        ConstellationManager, TemporalStateManager, LearningHub, AdaptiveEngine
    )
    
    # Test agent imports
    from pyframeworks_assistant.agents import (
        BaseAgent, InstructorAgent, CodeAssistantAgent
    )
    
    print("✅ Import tests passed!")


async def run_all_tests():
    """Run all tests."""
    print("🚀 Starting GAAPF Basic Functionality Tests\n")
    
    try:
        # Test imports first
        test_imports()
        
        # Test configurations
        test_user_profile()
        test_framework_configs()
        test_constellation_configs()
        
        # Test core components
        await test_constellation_manager()
        await test_temporal_state_manager()
        await test_learning_hub()
        await test_adaptive_engine()
        
        print("\n🎉 All tests passed! GAAPF system is working correctly.")
        print("\n📋 Test Summary:")
        print("✅ User Profile System")
        print("✅ Framework Configuration")
        print("✅ Constellation Configuration")
        print("✅ Constellation Manager")
        print("✅ Temporal State Manager")
        print("✅ Learning Hub")
        print("✅ Adaptive Engine")
        print("\n🚀 Ready to run: python run_cli.py")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = asyncio.run(run_all_tests())
    sys.exit(0 if success else 1)
