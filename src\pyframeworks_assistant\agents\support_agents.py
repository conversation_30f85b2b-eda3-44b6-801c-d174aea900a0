"""
Support domain agents for the GAAPF constellation system.

This module implements agents specialized in learner support:
- MentorAgent: Provides personalized guidance and learning strategy advice
- MotivationalCoachAgent: Offers encouragement and motivation
"""

from typing import Dict, Any, List, Optional
from datetime import datetime

from .base_agent import BaseAgent, AgentResponse, HandoffSuggestion, HandoffConfidence
from ..config.user_profiles import UserProfile
from ..config.framework_configs import SupportedFrameworks
from ..config.constellation_configs import AgentRole


class MentorAgent(BaseAgent):
    """
    Agent specialized in providing personalized guidance and learning strategy advice.
    
    Specializes in:
    - Learning strategy guidance
    - Personalized learning paths
    - Goal setting and planning
    - Learning optimization advice
    """
    
    def __init__(self, llm_client: Any, **kwargs):
        system_prompt = """You are a learning mentor for Python AI frameworks.
Your role is to provide personalized guidance and learning strategy advice.

Key responsibilities:
- Provide personalized learning guidance
- Help set realistic learning goals
- Suggest optimal learning strategies
- Offer career and skill development advice
- Help overcome learning challenges

Communication style:
- Be supportive and understanding
- Ask thoughtful questions about goals
- Provide personalized recommendations
- Encourage self-reflection
- Focus on long-term learning success"""

        super().__init__(
            agent_role=AgentRole.MENTOR,
            llm_client=llm_client,
            system_prompt=system_prompt,
            handoff_keywords=[
                "code", "example", "practice", "exercise", "implement",
                "assessment", "test", "evaluate", "progress"
            ],
            **kwargs
        )
    
    async def generate_response(
        self,
        user_message: str,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
        session_context: Dict[str, Any],
    ) -> AgentResponse:
        """Generate mentoring response."""
        start_time = datetime.utcnow()
        
        # Enhanced system prompt with user profile context
        enhanced_prompt = self._build_mentoring_prompt(user_profile, framework, module_id)
        
        messages = [
            {"role": "system", "content": enhanced_prompt},
            {"role": "user", "content": user_message}
        ]
        
        content = await self._call_llm(messages)
        
        self.add_to_history("user", user_message)
        self.add_to_history("assistant", content)
        
        handoff_suggestion = self.analyze_handoff_opportunity(user_message, session_context)
        processing_time = (datetime.utcnow() - start_time).total_seconds()
        
        return AgentResponse(
            agent_role=self.agent_role,
            content=content,
            response_type="mentoring",
            handoff_suggestion=handoff_suggestion,
            learning_objectives_addressed=["learning_strategy", "goal_setting", "self_reflection"],
            difficulty_level="adaptive",
            estimated_reading_time_minutes=len(content.split()) / 200,
            processing_time_seconds=processing_time,
        )
    
    def analyze_handoff_opportunity(
        self,
        user_message: str,
        current_context: Dict[str, Any],
    ) -> Optional[HandoffSuggestion]:
        """Analyze handoff opportunities from mentoring context."""
        message_lower = user_message.lower()
        
        # Check for specific learning requests
        if any(keyword in message_lower for keyword in ["learn", "practice", "exercise"]):
            return HandoffSuggestion(
                target_agent=AgentRole.PRACTICE_FACILITATOR,
                confidence=HandoffConfidence.MEDIUM,
                reason="User ready to engage in specific learning activities",
                context_data={"mentoring_guidance_provided": True}
            )
        
        # Check for code-related requests
        if any(keyword in message_lower for keyword in ["code", "example", "implement"]):
            return HandoffSuggestion(
                target_agent=AgentRole.CODE_ASSISTANT,
                confidence=HandoffConfidence.MEDIUM,
                reason="User needs practical implementation after mentoring discussion",
                context_data={"learning_direction_set": True}
            )
        
        # Check for assessment requests
        if any(keyword in message_lower for keyword in ["assess", "evaluate", "test", "progress"]):
            return HandoffSuggestion(
                target_agent=AgentRole.PROGRESS_TRACKER,
                confidence=HandoffConfidence.HIGH,
                reason="User wants to evaluate their learning progress",
                context_data={"mentoring_context": True}
            )
        
        return None
    
    def _build_mentoring_prompt(
        self,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
    ) -> str:
        """Build enhanced mentoring prompt with user context."""
        base_prompt = self.system_prompt
        
        # Add detailed user context for personalized mentoring
        context_additions = f"""

Detailed User Context for Personalized Mentoring:
- Name: {user_profile.name or 'Student'}
- Experience: {user_profile.programming_experience_years} years programming
- Python Level: {user_profile.python_skill_level}
- AI/ML Experience: {user_profile.ai_ml_experience}
- Learning Style: {user_profile.preferred_learning_style}
- Learning Pace: {user_profile.learning_pace}
- Goals: {', '.join(user_profile.learning_goals) if user_profile.learning_goals else 'General learning'}
- Current Framework: {framework}
- Total Learning Hours: {user_profile.total_learning_hours}
- Current Streak: {user_profile.current_streak_days} days
- Completed Modules: {len(user_profile.completed_modules)}

Provide highly personalized guidance based on this profile. Consider their experience level,
learning preferences, and goals when offering advice and suggestions.
"""
        
        return base_prompt + context_additions


class MotivationalCoachAgent(BaseAgent):
    """
    Agent specialized in providing encouragement and motivation.
    
    Specializes in:
    - Motivational support
    - Encouragement and positive reinforcement
    - Overcoming learning obstacles
    - Building confidence
    """
    
    def __init__(self, llm_client: Any, **kwargs):
        system_prompt = """You are a motivational coach for Python AI framework learners.
Your role is to provide encouragement, motivation, and positive reinforcement.

Key responsibilities:
- Provide motivational support and encouragement
- Help overcome learning obstacles and frustration
- Celebrate achievements and progress
- Build learner confidence
- Maintain positive learning momentum

Communication style:
- Be enthusiastic and encouraging
- Acknowledge challenges and struggles
- Celebrate small wins and progress
- Use positive, uplifting language
- Focus on growth mindset principles"""

        super().__init__(
            agent_role=AgentRole.MOTIVATIONAL_COACH,
            llm_client=llm_client,
            system_prompt=system_prompt,
            handoff_keywords=[
                "learn", "practice", "try", "continue", "next"
            ],
            **kwargs
        )
    
    async def generate_response(
        self,
        user_message: str,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
        session_context: Dict[str, Any],
    ) -> AgentResponse:
        """Generate motivational response."""
        start_time = datetime.utcnow()
        
        # Enhanced system prompt with motivational context
        enhanced_prompt = self._build_motivational_prompt(user_profile, session_context)
        
        messages = [
            {"role": "system", "content": enhanced_prompt},
            {"role": "user", "content": user_message}
        ]
        
        content = await self._call_llm(messages)
        
        self.add_to_history("user", user_message)
        self.add_to_history("assistant", content)
        
        handoff_suggestion = self.analyze_handoff_opportunity(user_message, session_context)
        processing_time = (datetime.utcnow() - start_time).total_seconds()
        
        return AgentResponse(
            agent_role=self.agent_role,
            content=content,
            response_type="motivational",
            handoff_suggestion=handoff_suggestion,
            learning_objectives_addressed=["motivation", "confidence_building", "persistence"],
            difficulty_level="supportive",
            estimated_reading_time_minutes=len(content.split()) / 220,  # Faster for motivational content
            processing_time_seconds=processing_time,
        )
    
    def analyze_handoff_opportunity(
        self,
        user_message: str,
        current_context: Dict[str, Any],
    ) -> Optional[HandoffSuggestion]:
        """Analyze handoff opportunities from motivational context."""
        message_lower = user_message.lower()
        
        # Check if user is ready to continue learning
        if any(keyword in message_lower for keyword in ["ready", "continue", "next", "let's go"]):
            # Determine best next agent based on context
            if "practice" in message_lower or "exercise" in message_lower:
                return HandoffSuggestion(
                    target_agent=AgentRole.PRACTICE_FACILITATOR,
                    confidence=HandoffConfidence.HIGH,
                    reason="User is motivated and ready for practice activities",
                    context_data={"motivation_boosted": True}
                )
            else:
                return HandoffSuggestion(
                    target_agent=AgentRole.INSTRUCTOR,
                    confidence=HandoffConfidence.MEDIUM,
                    reason="User is motivated and ready to continue learning",
                    context_data={"motivation_provided": True}
                )
        
        return None
    
    def _build_motivational_prompt(
        self,
        user_profile: UserProfile,
        session_context: Dict[str, Any],
    ) -> str:
        """Build enhanced motivational prompt with user achievements."""
        base_prompt = self.system_prompt
        
        # Add user achievement context for personalized motivation
        achievements = []
        if user_profile.total_learning_hours > 0:
            achievements.append(f"{user_profile.total_learning_hours:.1f} hours of learning")
        if user_profile.current_streak_days > 0:
            achievements.append(f"{user_profile.current_streak_days} day learning streak")
        if user_profile.completed_modules:
            achievements.append(f"{len(user_profile.completed_modules)} completed modules")
        
        context_additions = f"""

User Achievement Context for Personalized Motivation:
- Learning Journey: {user_profile.get_experience_level()}
- Recent Achievements: {', '.join(achievements) if achievements else 'Just getting started!'}
- Learning Goals: {', '.join(user_profile.learning_goals[:3]) if user_profile.learning_goals else 'Exploring AI frameworks'}
- Longest Streak: {user_profile.longest_streak_days} days

Use this context to provide personalized encouragement and celebrate their specific achievements.
Acknowledge their progress and help them stay motivated for continued learning.
"""
        
        return base_prompt + context_additions
