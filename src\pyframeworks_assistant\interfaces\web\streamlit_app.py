"""
Streamlit web interface for GAAPF demo.

This module provides a web-based demonstration interface for GAAPF
with visualization capabilities and interactive features.
"""

import sys
from pathlib import Path
import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
import pandas as pd
from datetime import datetime, timedelta

# Add src to path for imports
src_path = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(src_path))

from pyframeworks_assistant.config.user_profiles import (
    UserProfile, SkillLevel, LearningPace, LearningStyle
)
from pyframeworks_assistant.config.framework_configs import (
    SupportedFrameworks, get_framework_config, get_available_frameworks
)
from pyframeworks_assistant.config.constellation_configs import (
    ConstellationType, get_constellation_config, get_available_constellations
)

# Page configuration
st.set_page_config(
    page_title="GAAPF - Guidance AI Agent for Python Framework",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .constellation-card {
        border: 1px solid #ddd;
        border-radius: 10px;
        padding: 1rem;
        margin: 0.5rem 0;
        background-color: #f8f9fa;
    }
    .metric-card {
        background-color: #e3f2fd;
        padding: 1rem;
        border-radius: 8px;
        text-align: center;
    }
</style>
""", unsafe_allow_html=True)

def main():
    """Main Streamlit application."""
    
    # Header
    st.markdown('<h1 class="main-header">🤖 GAAPF Demo</h1>', unsafe_allow_html=True)
    st.markdown("**Guidance AI Agent for Python Framework - Interactive Demo**")
    
    # Sidebar navigation
    st.sidebar.title("🧭 Navigation")
    page = st.sidebar.selectbox(
        "Choose a page:",
        ["🏠 Home", "👤 User Profile", "⭐ Constellations", "📚 Frameworks", "📊 Analytics", "💬 Chat Demo"]
    )
    
    if page == "🏠 Home":
        show_home_page()
    elif page == "👤 User Profile":
        show_user_profile_page()
    elif page == "⭐ Constellations":
        show_constellations_page()
    elif page == "📚 Frameworks":
        show_frameworks_page()
    elif page == "📊 Analytics":
        show_analytics_page()
    elif page == "💬 Chat Demo":
        show_chat_demo_page()

def show_home_page():
    """Display home page with system overview."""
    
    st.markdown("## 🌟 Welcome to GAAPF")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        ### 🎯 What is GAAPF?
        
        GAAPF (Guidance AI Agent for Python Framework) is an innovative adaptive multi-agent learning system designed to provide personalized, interactive learning experiences for Python AI frameworks.
        
        **Key Features:**
        - 🔗 **Adaptive Learning Constellations**: Dynamic agent teams that adapt to your learning style
        - 📊 **Temporal Optimization**: Learns from your patterns to improve over time
        - 🎯 **Personalized Experience**: Tailored to your skill level and goals
        - 🤖 **Multi-Agent Intelligence**: Specialized agents for different learning needs
        """)
    
    with col2:
        st.markdown("""
        ### 🚀 Getting Started
        
        1. **Create Your Profile**: Set up your learning preferences and goals
        2. **Choose a Framework**: Select from LangChain, LangGraph, and more
        3. **Start Learning**: Engage with AI agents tailored to your needs
        4. **Track Progress**: Monitor your learning journey with analytics
        
        **Available Interfaces:**
        - 💻 **CLI**: Full experience with real LLM integration
        - 🌐 **Web**: This demo interface with visualizations
        - 🔌 **API**: REST API for custom integrations
        """)
    
    # System architecture diagram
    st.markdown("## 🏗️ System Architecture")
    
    # Create a simple architecture visualization
    architecture_data = {
        "Layer": ["User Interface", "Agent Management", "Core Orchestration", "LLM Integration", "Storage"],
        "Components": [
            "CLI, Web, API",
            "Adaptive Constellations",
            "Temporal State, Learning Hub",
            "OpenAI, Anthropic, Google",
            "User Profiles, Memory"
        ],
        "Count": [3, 12, 4, 3, 5]
    }
    
    fig = px.bar(
        architecture_data,
        x="Layer",
        y="Count",
        title="GAAPF System Components by Layer",
        color="Count",
        color_continuous_scale="viridis"
    )
    st.plotly_chart(fig, use_container_width=True)
    
    # Quick stats
    st.markdown("## 📈 System Capabilities")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.markdown('<div class="metric-card"><h3>8</h3><p>Constellation Types</p></div>', unsafe_allow_html=True)
    
    with col2:
        st.markdown('<div class="metric-card"><h3>12+</h3><p>Specialized Agents</p></div>', unsafe_allow_html=True)
    
    with col3:
        st.markdown('<div class="metric-card"><h3>2+</h3><p>Supported Frameworks</p></div>', unsafe_allow_html=True)
    
    with col4:
        st.markdown('<div class="metric-card"><h3>3</h3><p>Interface Options</p></div>', unsafe_allow_html=True)

def show_user_profile_page():
    """Display user profile creation and management."""
    
    st.markdown("## 👤 User Profile Management")
    
    st.markdown("Create or modify your learning profile to get personalized recommendations.")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### Basic Information")
        
        name = st.text_input("Name", value="Demo User")
        user_id = st.text_input("Username", value="demo_user")
        
        st.markdown("### Experience Level")
        
        programming_years = st.slider("Programming Experience (years)", 0, 20, 3)
        python_skill = st.selectbox("Python Skill Level", [level.value for level in SkillLevel], index=2)
        ai_experience = st.selectbox("AI/ML Experience", [level.value for level in SkillLevel], index=1)
    
    with col2:
        st.markdown("### Learning Preferences")
        
        learning_pace = st.selectbox("Learning Pace", [pace.value for pace in LearningPace], index=1)
        learning_style = st.selectbox("Learning Style", [style.value for style in LearningStyle], index=3)
        
        st.markdown("### Goals")
        
        goals_text = st.text_area("Learning Goals (one per line)", 
                                 value="Learn LangChain\nBuild RAG applications\nMaster AI agents")
        goals = [goal.strip() for goal in goals_text.split('\n') if goal.strip()]
    
    # Create profile
    if st.button("Create/Update Profile"):
        profile = UserProfile(
            user_id=user_id,
            name=name,
            programming_experience_years=programming_years,
            python_skill_level=SkillLevel(python_skill),
            ai_ml_experience=SkillLevel(ai_experience),
            learning_pace=LearningPace(learning_pace),
            preferred_learning_style=LearningStyle(learning_style),
            learning_goals=goals
        )
        
        st.success("Profile created successfully!")
        
        # Display profile summary
        st.markdown("### Profile Summary")
        
        profile_data = {
            "Attribute": ["Experience Level", "Python Skill", "AI/ML Experience", "Learning Pace", "Learning Style"],
            "Value": [
                profile.get_experience_level(),
                profile.python_skill_level.value.title(),
                profile.ai_ml_experience.value.title(),
                profile.learning_pace.value.title(),
                profile.preferred_learning_style.value.replace('_', ' ').title()
            ]
        }
        
        df = pd.DataFrame(profile_data)
        st.table(df)

def show_constellations_page():
    """Display constellation types and configurations."""
    
    st.markdown("## ⭐ Learning Constellations")
    
    st.markdown("""
    GAAPF uses 8 specialized constellation types, each optimized for different learning scenarios.
    Each constellation consists of carefully selected agents that work together to provide the best learning experience.
    """)
    
    # Get all constellation types
    constellations = get_available_constellations()
    
    # Create tabs for each constellation
    tabs = st.tabs([const.value.replace('_', ' ').title() for const in constellations])
    
    for i, constellation_type in enumerate(constellations):
        with tabs[i]:
            config = get_constellation_config(constellation_type)
            
            col1, col2 = st.columns([2, 1])
            
            with col1:
                st.markdown(f"### {config.display_name}")
                st.markdown(f"**Focus:** {config.focus_area}")
                st.markdown(f"**Description:** {config.description}")
                
                st.markdown("#### Primary Agents")
                for agent in config.primary_agents:
                    st.markdown(f"- 🤖 {agent.value.replace('_', ' ').title()}")
                
                if config.support_agents:
                    st.markdown("#### Support Agents")
                    for agent in config.support_agents:
                        st.markdown(f"- 🔧 {agent.value.replace('_', ' ').title()}")
            
            with col2:
                st.markdown("#### Configuration")
                st.metric("Max Concurrent Agents", config.max_concurrent_agents)
                st.metric("Theoretical Weight", f"{config.theoretical_weight:.1%}")
                st.metric("Practical Weight", f"{config.practical_weight:.1%}")
                st.metric("Interaction Intensity", f"{config.interaction_intensity:.1%}")
                
                st.markdown("#### Best For")
                for style in config.best_for_learning_styles:
                    st.markdown(f"- {style.replace('_', ' ').title()}")

def show_frameworks_page():
    """Display supported frameworks and modules."""
    
    st.markdown("## 📚 Supported Frameworks")
    
    frameworks = get_available_frameworks()
    
    for framework in frameworks:
        config = get_framework_config(framework)
        
        with st.expander(f"{config.display_name} - {config.version}"):
            col1, col2 = st.columns([2, 1])
            
            with col1:
                st.markdown(f"**Description:** {config.description}")
                st.markdown(f"**Documentation:** [{config.official_docs_url}]({config.official_docs_url})")
                st.markdown(f"**Installation:** `{config.installation_command}`")
                
                if config.key_concepts:
                    st.markdown("**Key Concepts:**")
                    for concept in config.key_concepts:
                        st.markdown(f"- {concept}")
            
            with col2:
                st.metric("Total Modules", len(config.modules))
                st.metric("Estimated Hours", f"{config.total_estimated_hours}")
                st.metric("Status", "✅ Ready" if config.is_fully_supported else "🔄 Coming Soon")
            
            # Display modules
            if config.modules:
                st.markdown("### Learning Modules")
                
                modules_data = []
                for module in config.modules:
                    modules_data.append({
                        "Module": module.title,
                        "Difficulty": module.difficulty.value.title(),
                        "Hours": module.estimated_hours,
                        "Exercises": module.practical_exercises,
                        "Project": "✅" if module.has_project else "❌"
                    })
                
                df = pd.DataFrame(modules_data)
                st.dataframe(df, use_container_width=True)

def show_analytics_page():
    """Display analytics and learning insights."""
    
    st.markdown("## 📊 Learning Analytics")
    
    st.markdown("This page shows sample analytics that would be generated from actual learning sessions.")
    
    # Generate sample data
    dates = pd.date_range(start='2024-01-01', end='2024-01-31', freq='D')
    sample_data = pd.DataFrame({
        'Date': dates,
        'Effectiveness': [0.6 + 0.3 * (i / len(dates)) + 0.1 * (i % 7) / 7 for i in range(len(dates))],
        'Engagement': [0.5 + 0.4 * (i / len(dates)) + 0.1 * ((i + 3) % 5) / 5 for i in range(len(dates))],
        'Session_Length': [25 + 10 * (i % 3) + 5 * (i % 7) for i in range(len(dates))]
    })
    
    col1, col2 = st.columns(2)
    
    with col1:
        # Effectiveness over time
        fig1 = px.line(sample_data, x='Date', y='Effectiveness', 
                      title='Learning Effectiveness Over Time',
                      labels={'Effectiveness': 'Effectiveness Score'})
        fig1.update_traces(line_color='#1f77b4')
        st.plotly_chart(fig1, use_container_width=True)
        
        # Session length distribution
        fig3 = px.histogram(sample_data, x='Session_Length', nbins=10,
                           title='Session Length Distribution',
                           labels={'Session_Length': 'Session Length (minutes)'})
        st.plotly_chart(fig3, use_container_width=True)
    
    with col2:
        # Engagement over time
        fig2 = px.line(sample_data, x='Date', y='Engagement',
                      title='Engagement Score Over Time',
                      labels={'Engagement': 'Engagement Score'})
        fig2.update_traces(line_color='#ff7f0e')
        st.plotly_chart(fig2, use_container_width=True)
        
        # Constellation performance
        constellation_data = pd.DataFrame({
            'Constellation': ['Knowledge Intensive', 'Hands-On Focused', 'Theory-Practice Balanced', 'Project Oriented'],
            'Effectiveness': [0.75, 0.85, 0.80, 0.78],
            'Sessions': [12, 18, 15, 8]
        })
        
        fig4 = px.bar(constellation_data, x='Constellation', y='Effectiveness',
                     title='Constellation Performance',
                     color='Sessions', color_continuous_scale='viridis')
        st.plotly_chart(fig4, use_container_width=True)
    
    # Summary metrics
    st.markdown("### Summary Metrics")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Sessions", "31", delta="5")
    
    with col2:
        st.metric("Learning Hours", "24.5", delta="3.2")
    
    with col3:
        st.metric("Avg Effectiveness", "78%", delta="12%")
    
    with col4:
        st.metric("Current Streak", "7 days", delta="2")

def show_chat_demo_page():
    """Display chat demo interface."""
    
    st.markdown("## 💬 Chat Demo")
    
    st.markdown("""
    This is a demo of the GAAPF chat interface. In the full CLI version, 
    you would interact with real AI agents powered by LLMs like GPT, Claude, or Gemini.
    """)
    
    # Initialize chat history
    if "messages" not in st.session_state:
        st.session_state.messages = [
            {"role": "assistant", "content": "Hello! I'm your AI learning assistant. What would you like to learn about today?"}
        ]
    
    # Display chat messages
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])
    
    # Chat input
    if prompt := st.chat_input("Ask me anything about Python AI frameworks..."):
        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": prompt})
        
        # Display user message
        with st.chat_message("user"):
            st.markdown(prompt)
        
        # Generate assistant response (mock)
        with st.chat_message("assistant"):
            response = generate_demo_response(prompt)
            st.markdown(response)
        
        # Add assistant response to chat history
        st.session_state.messages.append({"role": "assistant", "content": response})

def generate_demo_response(prompt: str) -> str:
    """Generate demo response for chat interface."""
    
    prompt_lower = prompt.lower()
    
    if "langchain" in prompt_lower:
        return """🔗 **LangChain** is a powerful framework for building applications with Large Language Models!

**Key Components:**
- **LLMs**: Interface with language models
- **Prompts**: Template your inputs
- **Chains**: Connect components together
- **Agents**: Let LLMs make decisions
- **Memory**: Maintain conversation state

**Quick Example:**
```python
from langchain.llms import OpenAI
from langchain.prompts import PromptTemplate

llm = OpenAI()
prompt = PromptTemplate(
    input_variables=["topic"],
    template="Explain {topic} simply"
)
chain = prompt | llm
```

Would you like me to explain any specific component in detail? 🤖"""
    
    elif "constellation" in prompt_lower:
        return """⭐ **Learning Constellations** are GAAPF's secret sauce!

Each constellation is a team of specialized AI agents:

🧠 **Knowledge Intensive**: Perfect for theory and concepts
⚡ **Hands-On Focused**: Great for coding and practice  
⚖️ **Theory-Practice Balanced**: Best of both worlds
🔬 **Research Intensive**: Deep dives into advanced topics

The system automatically selects the best constellation based on:
- Your learning style
- Current topic
- Historical effectiveness
- Real-time adaptation

Which constellation sounds most interesting to you?"""
    
    elif "help" in prompt_lower or "start" in prompt_lower:
        return """🚀 **Welcome to GAAPF!** Here's how to get started:

1. **Tell me your experience level** - Are you new to Python, AI, or programming?
2. **Choose what to learn** - LangChain, LangGraph, or specific concepts
3. **Pick your style** - Do you prefer theory, hands-on coding, or mixed?

**Popular starting points:**
- "What is LangChain?" - Great for beginners
- "Show me a code example" - For hands-on learners  
- "I want to build a chatbot" - Project-oriented learning

**Pro tip:** The more specific you are, the better I can help! What interests you most? 🎯"""
    
    else:
        return f"""I understand you're asking about: "{prompt}"

This is a great question! In the full GAAPF system, I would:

🔍 **Analyze your question** to understand the learning context
🤖 **Select the best agent** (Instructor, Code Assistant, etc.)
📚 **Provide personalized content** based on your profile
🔄 **Adapt in real-time** based on your responses

**To experience the full GAAPF system:**
- Run `python run_cli.py` for the complete CLI experience
- Set up your API keys for real LLM integration
- Create your learning profile for personalization

Would you like me to explain any specific aspect of GAAPF? 🚀"""

if __name__ == "__main__":
    main()
