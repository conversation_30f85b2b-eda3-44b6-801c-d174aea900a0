"""
Framework configuration management for supported AI frameworks.

This module defines configurations for different AI frameworks including
learning modules, prerequisites, and framework-specific settings.
"""

from enum import Enum
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field


class SupportedFrameworks(str, Enum):
    """Enumeration of supported AI frameworks."""
    LANGCHAIN = "langchain"
    LANGGRAPH = "langgraph"
    CREWAI = "crewai"  # Coming soon
    AUTOGEN = "autogen"  # Coming soon
    LLAMAINDEX = "llamaindex"  # Coming soon


class ModuleDifficulty(str, Enum):
    """Module difficulty levels."""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"


class LearningModule(BaseModel):
    """Individual learning module configuration."""
    
    module_id: str = Field(..., description="Unique module identifier")
    title: str = Field(..., description="Module title")
    description: str = Field(..., description="Module description")
    difficulty: ModuleDifficulty = Field(..., description="Module difficulty level")
    estimated_hours: float = Field(..., ge=0.5, le=20, description="Estimated completion time")
    prerequisites: List[str] = Field(default_factory=list, description="Required prerequisite modules")
    learning_objectives: List[str] = Field(default_factory=list, description="Learning objectives")
    topics: List[str] = Field(default_factory=list, description="Topics covered")
    practical_exercises: int = Field(0, ge=0, description="Number of practical exercises")
    has_project: bool = Field(False, description="Whether module includes a project")
    
    class Config:
        use_enum_values = True


class FrameworkConfig(BaseModel):
    """Configuration for a specific AI framework."""
    
    framework: SupportedFrameworks = Field(..., description="Framework identifier")
    display_name: str = Field(..., description="Human-readable framework name")
    version: str = Field(..., description="Supported framework version")
    description: str = Field(..., description="Framework description")
    official_docs_url: str = Field(..., description="Official documentation URL")
    
    # Learning Configuration
    modules: List[LearningModule] = Field(default_factory=list, description="Available learning modules")
    total_estimated_hours: float = Field(0.0, ge=0, description="Total estimated learning time")
    
    # Prerequisites
    python_skill_required: str = Field("beginner", description="Minimum Python skill level")
    programming_experience_required: int = Field(0, ge=0, description="Minimum programming experience years")
    
    # Framework-specific settings
    installation_command: str = Field("", description="Installation command")
    import_examples: List[str] = Field(default_factory=list, description="Common import examples")
    key_concepts: List[str] = Field(default_factory=list, description="Key framework concepts")
    
    # Status
    is_fully_supported: bool = Field(True, description="Whether framework is fully supported")
    support_status: str = Field("stable", description="Support status")
    
    class Config:
        use_enum_values = True


# Framework Configurations
LANGCHAIN_CONFIG = FrameworkConfig(
    framework=SupportedFrameworks.LANGCHAIN,
    display_name="LangChain",
    version="0.3.25+",
    description="A framework for developing applications powered by language models",
    official_docs_url="https://python.langchain.com/",
    python_skill_required="beginner",
    programming_experience_required=0,
    installation_command="pip install langchain",
    import_examples=[
        "from langchain.llms import OpenAI",
        "from langchain.chains import LLMChain",
        "from langchain.prompts import PromptTemplate",
    ],
    key_concepts=[
        "LLMs and Chat Models",
        "Prompts and Prompt Templates", 
        "Chains",
        "Memory",
        "Agents and Tools",
        "Retrieval Augmented Generation (RAG)",
        "Vector Stores",
        "Document Loaders",
    ],
    modules=[
        LearningModule(
            module_id="lc_basics",
            title="LangChain Fundamentals",
            description="Learn the core concepts and basic usage of LangChain",
            difficulty=ModuleDifficulty.BEGINNER,
            estimated_hours=4.0,
            learning_objectives=[
                "Understand LangChain architecture",
                "Work with LLMs and Chat Models",
                "Create and use Prompt Templates",
                "Build simple chains",
            ],
            topics=[
                "LangChain Overview",
                "LLM Integration",
                "Prompt Engineering",
                "Basic Chains",
            ],
            practical_exercises=5,
            has_project=True,
        ),
        LearningModule(
            module_id="lc_intermediate",
            title="Advanced LangChain Features",
            description="Explore advanced LangChain capabilities including memory, agents, and RAG",
            difficulty=ModuleDifficulty.INTERMEDIATE,
            estimated_hours=6.0,
            prerequisites=["lc_basics"],
            learning_objectives=[
                "Implement conversation memory",
                "Build intelligent agents",
                "Create RAG applications",
                "Work with vector databases",
            ],
            topics=[
                "Memory Systems",
                "Agents and Tools",
                "Vector Stores",
                "RAG Implementation",
                "Document Processing",
            ],
            practical_exercises=8,
            has_project=True,
        ),
        LearningModule(
            module_id="lc_advanced",
            title="LangChain Production Systems",
            description="Build production-ready applications with LangChain",
            difficulty=ModuleDifficulty.ADVANCED,
            estimated_hours=8.0,
            prerequisites=["lc_intermediate"],
            learning_objectives=[
                "Design scalable LangChain applications",
                "Implement error handling and monitoring",
                "Optimize performance",
                "Deploy to production",
            ],
            topics=[
                "Production Architecture",
                "Error Handling",
                "Performance Optimization",
                "Monitoring and Logging",
                "Deployment Strategies",
            ],
            practical_exercises=6,
            has_project=True,
        ),
    ],
    total_estimated_hours=18.0,
    is_fully_supported=True,
    support_status="stable",
)

LANGGRAPH_CONFIG = FrameworkConfig(
    framework=SupportedFrameworks.LANGGRAPH,
    display_name="LangGraph",
    version="0.4.7+",
    description="A library for building stateful, multi-actor applications with LLMs",
    official_docs_url="https://langchain-ai.github.io/langgraph/",
    python_skill_required="intermediate",
    programming_experience_required=1,
    installation_command="pip install langgraph",
    import_examples=[
        "from langgraph.graph import StateGraph",
        "from langgraph.prebuilt import ToolExecutor",
        "from langgraph.checkpoint.sqlite import SqliteSaver",
    ],
    key_concepts=[
        "State Graphs",
        "Nodes and Edges",
        "Conditional Routing",
        "Persistence and Checkpointing",
        "Multi-Agent Workflows",
        "Tool Integration",
        "Human-in-the-Loop",
    ],
    modules=[
        LearningModule(
            module_id="lg_fundamentals",
            title="LangGraph Fundamentals and Multi-Agent Systems",
            description="Master LangGraph for building complex, stateful AI applications",
            difficulty=ModuleDifficulty.INTERMEDIATE,
            estimated_hours=15.0,
            prerequisites=["lc_intermediate"],
            learning_objectives=[
                "Understand LangGraph architecture",
                "Build stateful applications",
                "Implement multi-agent workflows",
                "Use persistence and checkpointing",
                "Create human-in-the-loop systems",
            ],
            topics=[
                "LangGraph Overview",
                "State Management",
                "Graph Construction",
                "Multi-Agent Coordination",
                "Persistence Strategies",
                "Tool Integration",
                "Human Interaction Patterns",
            ],
            practical_exercises=12,
            has_project=True,
        ),
    ],
    total_estimated_hours=15.0,
    is_fully_supported=True,
    support_status="stable",
)

# Framework registry
FRAMEWORK_CONFIGS: Dict[SupportedFrameworks, FrameworkConfig] = {
    SupportedFrameworks.LANGCHAIN: LANGCHAIN_CONFIG,
    SupportedFrameworks.LANGGRAPH: LANGGRAPH_CONFIG,
}


def get_framework_config(framework: SupportedFrameworks) -> FrameworkConfig:
    """Get configuration for a specific framework."""
    if framework not in FRAMEWORK_CONFIGS:
        raise ValueError(f"Framework {framework} is not supported")
    return FRAMEWORK_CONFIGS[framework]


def get_available_frameworks() -> List[SupportedFrameworks]:
    """Get list of available frameworks."""
    return list(FRAMEWORK_CONFIGS.keys())


def get_framework_by_module(module_id: str) -> Optional[SupportedFrameworks]:
    """Find framework that contains a specific module."""
    for framework, config in FRAMEWORK_CONFIGS.items():
        if any(module.module_id == module_id for module in config.modules):
            return framework
    return None


def get_module_by_id(module_id: str) -> Optional[LearningModule]:
    """Get a specific learning module by ID."""
    for config in FRAMEWORK_CONFIGS.values():
        for module in config.modules:
            if module.module_id == module_id:
                return module
    return None
