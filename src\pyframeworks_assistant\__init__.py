"""
GAAPF - Guidance AI Agent for Python Framework

An Adaptive Multi-Agent Learning System for AI Framework Education
"""

__version__ = "1.0.0"
__author__ = "GAAPF Team"
__email__ = "<EMAIL>"

from .config.user_profiles import UserProfile, SkillLevel, LearningPace, LearningStyle
from .config.framework_configs import SupportedFrameworks
from .config.constellation_configs import ConstellationType
from .core.constellation import ConstellationManager
from .core.temporal_state import TemporalStateManager

__all__ = [
    "UserProfile",
    "SkillLevel", 
    "LearningPace",
    "LearningStyle",
    "SupportedFrameworks",
    "ConstellationType",
    "ConstellationManager",
    "TemporalStateManager",
]
