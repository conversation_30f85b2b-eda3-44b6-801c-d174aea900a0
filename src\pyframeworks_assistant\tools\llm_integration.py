"""
LLM integration utilities for GAAPF.

This module provides integration with various LLM providers including
OpenAI GPT, Anthropic Claude, and Google Gemini.
"""

import os
from typing import Dict, Any, List, Optional, Union
from enum import Enum
from pydantic import BaseModel, Field

try:
    from langchain_openai import <PERSON>t<PERSON>penAI
    from langchain_anthropic import <PERSON><PERSON><PERSON><PERSON><PERSON>
    from langchain_google_genai import ChatGoogleGenerativeAI
    from langchain_core.messages import HumanMessage, SystemMessage
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False


class LLMProvider(str, Enum):
    """Supported LLM providers."""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    MOCK = "mock"


class LLMConfig(BaseModel):
    """Configuration for LLM integration."""
    
    provider: LLMProvider = Field(..., description="LLM provider")
    model_name: str = Field(..., description="Model name")
    api_key: Optional[str] = Field(None, description="API key")
    temperature: float = Field(0.7, description="Temperature setting")
    max_tokens: Optional[int] = Field(None, description="Maximum tokens")
    
    class Config:
        use_enum_values = True


class LLMIntegration:
    """
    LLM integration manager for GAAPF.
    
    Provides unified interface for interacting with different LLM providers
    while handling authentication, rate limiting, and error handling.
    """
    
    def __init__(self, config: Optional[LLMConfig] = None):
        """Initialize LLM integration."""
        self.config = config or self._auto_detect_config()
        self.client = self._initialize_client()
    
    def _auto_detect_config(self) -> LLMConfig:
        """Auto-detect available LLM configuration."""
        
        # Check for API keys in environment
        if os.getenv("OPENAI_API_KEY"):
            return LLMConfig(
                provider=LLMProvider.OPENAI,
                model_name="gpt-3.5-turbo",
                api_key=os.getenv("OPENAI_API_KEY")
            )
        elif os.getenv("ANTHROPIC_API_KEY"):
            return LLMConfig(
                provider=LLMProvider.ANTHROPIC,
                model_name="claude-3-sonnet-20240229",
                api_key=os.getenv("ANTHROPIC_API_KEY")
            )
        elif os.getenv("GOOGLE_API_KEY"):
            return LLMConfig(
                provider=LLMProvider.GOOGLE,
                model_name="gemini-pro",
                api_key=os.getenv("GOOGLE_API_KEY")
            )
        else:
            # Fall back to mock provider
            return LLMConfig(
                provider=LLMProvider.MOCK,
                model_name="mock-model"
            )
    
    def _initialize_client(self) -> Any:
        """Initialize the LLM client based on configuration."""
        
        if not LANGCHAIN_AVAILABLE:
            print("Warning: LangChain not available, using mock client")
            return None
        
        try:
            if self.config.provider == LLMProvider.OPENAI:
                return ChatOpenAI(
                    model=self.config.model_name,
                    api_key=self.config.api_key,
                    temperature=self.config.temperature,
                    max_tokens=self.config.max_tokens
                )
            elif self.config.provider == LLMProvider.ANTHROPIC:
                return ChatAnthropic(
                    model=self.config.model_name,
                    api_key=self.config.api_key,
                    temperature=self.config.temperature,
                    max_tokens=self.config.max_tokens
                )
            elif self.config.provider == LLMProvider.GOOGLE:
                return ChatGoogleGenerativeAI(
                    model=self.config.model_name,
                    google_api_key=self.config.api_key,
                    temperature=self.config.temperature,
                    max_output_tokens=self.config.max_tokens
                )
            else:
                return None  # Mock client
                
        except Exception as e:
            print(f"Warning: Failed to initialize {self.config.provider} client: {e}")
            return None
    
    async def generate_response(
        self,
        messages: List[Dict[str, str]],
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> str:
        """Generate response from LLM."""
        
        if not self.client:
            return self._generate_mock_response(messages)
        
        try:
            # Convert messages to LangChain format
            langchain_messages = []
            
            if system_prompt:
                langchain_messages.append(SystemMessage(content=system_prompt))
            
            for msg in messages:
                if msg["role"] == "user":
                    langchain_messages.append(HumanMessage(content=msg["content"]))
                elif msg["role"] == "system":
                    langchain_messages.append(SystemMessage(content=msg["content"]))
            
            # Generate response
            response = await self.client.ainvoke(langchain_messages)
            return response.content
            
        except Exception as e:
            print(f"Error generating LLM response: {e}")
            return self._generate_mock_response(messages)
    
    def _generate_mock_response(self, messages: List[Dict[str, str]]) -> str:
        """Generate mock response for testing/demo purposes."""
        
        last_message = messages[-1]["content"] if messages else ""
        
        # Simple keyword-based mock responses
        if "langchain" in last_message.lower():
            return """LangChain is a powerful framework for building applications with Large Language Models (LLMs). Here are the key concepts:

## Core Components:
1. **LLMs and Chat Models** - Interface with language models
2. **Prompts** - Templates for structuring inputs
3. **Chains** - Combine multiple components
4. **Agents** - Use LLMs to decide actions
5. **Memory** - Persist state between calls

## Basic Example:
```python
from langchain.llms import OpenAI
from langchain.prompts import PromptTemplate

llm = OpenAI(temperature=0.7)
prompt = PromptTemplate(
    input_variables=["topic"],
    template="Explain {topic} in simple terms"
)

chain = prompt | llm
result = chain.invoke({"topic": "machine learning"})
```

Would you like me to explain any of these concepts in more detail?"""
        
        elif "example" in last_message.lower() or "code" in last_message.lower():
            return """Here's a practical code example:

```python
# Simple LangChain example
from langchain.llms import OpenAI
from langchain.chains import LLMChain
from langchain.prompts import PromptTemplate

# Initialize the LLM
llm = OpenAI(temperature=0.7)

# Create a prompt template
prompt = PromptTemplate(
    input_variables=["question"],
    template="Answer this question clearly: {question}"
)

# Create a chain
chain = LLMChain(llm=llm, prompt=prompt)

# Use the chain
response = chain.run("What is artificial intelligence?")
print(response)
```

This example shows the basic pattern of LangChain: LLM + Prompt + Chain = Powerful Application!

Try running this code and let me know if you have questions!"""
        
        elif "practice" in last_message.lower() or "exercise" in last_message.lower():
            return """Great! Let's practice with some hands-on exercises:

## Exercise 1: Basic Chain
Create a simple chain that takes a programming concept and explains it with an analogy.

## Exercise 2: Prompt Engineering
Experiment with different prompt templates to see how they affect the output.

## Exercise 3: Memory Integration
Add conversation memory to maintain context across multiple interactions.

## Your Task:
Pick one exercise and try implementing it. I'll help you debug any issues you encounter!

Which exercise interests you most?"""
        
        else:
            return f"""I understand you're asking about: "{last_message}"

This is a great question! Let me help you learn about this topic step by step.

Based on your question, I recommend we explore:
1. The fundamental concepts involved
2. Practical examples and use cases  
3. Hands-on exercises to reinforce learning

Would you like me to start with an explanation, show you some code examples, or create a practice exercise?

I'm here to adapt to your learning style and help you master these concepts!"""
    
    def get_provider_info(self) -> Dict[str, Any]:
        """Get information about the current LLM provider."""
        
        return {
            "provider": self.config.provider,
            "model": self.config.model_name,
            "available": self.client is not None,
            "temperature": self.config.temperature,
            "max_tokens": self.config.max_tokens,
        }
    
    def is_available(self) -> bool:
        """Check if LLM integration is available."""
        return self.client is not None or self.config.provider == LLMProvider.MOCK
    
    def estimate_tokens(self, text: str) -> int:
        """Estimate token count for text (rough approximation)."""
        # Very rough estimation: ~4 characters per token
        return len(text) // 4
    
    def validate_api_key(self) -> bool:
        """Validate API key configuration."""
        
        if self.config.provider == LLMProvider.MOCK:
            return True
        
        if not self.config.api_key:
            return False
        
        # Basic validation - check if key looks valid
        if self.config.provider == LLMProvider.OPENAI:
            return self.config.api_key.startswith("sk-")
        elif self.config.provider == LLMProvider.ANTHROPIC:
            return self.config.api_key.startswith("sk-ant-")
        elif self.config.provider == LLMProvider.GOOGLE:
            return len(self.config.api_key) > 20
        
        return True
