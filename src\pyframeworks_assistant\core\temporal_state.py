"""
Temporal state management for learning optimization.

This module implements the temporal optimization system that learns from
historical interactions to improve future constellation selection and
agent coordination effectiveness.
"""

import json
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from pydantic import BaseModel, Field
from pathlib import Path

from ..config.user_profiles import UserProfile
from ..config.framework_configs import SupportedFrameworks
from ..config.constellation_configs import ConstellationType


class LearningMetrics(BaseModel):
    """Metrics for measuring learning effectiveness."""
    
    comprehension_score: float = Field(0.0, ge=0.0, le=1.0, description="Understanding level")
    engagement_score: float = Field(0.0, ge=0.0, le=1.0, description="User engagement level")
    completion_rate: float = Field(0.0, ge=0.0, le=1.0, description="Task completion rate")
    satisfaction_score: float = Field(0.0, ge=0.0, le=1.0, description="User satisfaction")
    time_efficiency: float = Field(0.0, ge=0.0, le=1.0, description="Learning speed efficiency")
    
    def calculate_overall_effectiveness(self) -> float:
        """Calculate overall effectiveness score."""
        # Weighted combination of metrics
        weights = {
            'comprehension': 0.3,
            'engagement': 0.2,
            'completion': 0.2,
            'satisfaction': 0.15,
            'efficiency': 0.15,
        }
        
        return (
            self.comprehension_score * weights['comprehension'] +
            self.engagement_score * weights['engagement'] +
            self.completion_rate * weights['completion'] +
            self.satisfaction_score * weights['satisfaction'] +
            self.time_efficiency * weights['efficiency']
        )


class LearningSession(BaseModel):
    """Represents a completed learning session with metrics."""
    
    session_id: str = Field(..., description="Unique session identifier")
    user_id: str = Field(..., description="User identifier")
    constellation_type: ConstellationType = Field(..., description="Constellation type used")
    framework: SupportedFrameworks = Field(..., description="Framework studied")
    module_id: str = Field(..., description="Module identifier")
    
    # Session Data
    start_time: datetime = Field(..., description="Session start time")
    end_time: datetime = Field(..., description="Session end time")
    duration_minutes: float = Field(..., description="Session duration in minutes")
    
    # Interaction Data
    total_interactions: int = Field(0, description="Total user interactions")
    agent_handoffs: int = Field(0, description="Number of agent handoffs")
    primary_agents_used: List[str] = Field(default_factory=list, description="Primary agents used")
    
    # Learning Metrics
    metrics: LearningMetrics = Field(default_factory=LearningMetrics, description="Learning effectiveness metrics")
    
    # Context Data
    user_skill_level: str = Field("", description="User skill level at time of session")
    learning_style: str = Field("", description="User learning style")
    learning_pace: str = Field("", description="User learning pace")
    
    class Config:
        use_enum_values = True


class ConstellationEffectiveness(BaseModel):
    """Tracks effectiveness of constellation types for specific contexts."""
    
    constellation_type: ConstellationType = Field(..., description="Constellation type")
    context_key: str = Field(..., description="Context identifier")
    
    # Effectiveness Data
    total_sessions: int = Field(0, description="Total sessions with this constellation")
    average_effectiveness: float = Field(0.0, description="Average effectiveness score")
    effectiveness_trend: List[float] = Field(default_factory=list, description="Recent effectiveness scores")
    
    # Context Matching
    user_profiles_matched: List[str] = Field(default_factory=list, description="User profile patterns")
    frameworks_used: List[str] = Field(default_factory=list, description="Frameworks used")
    
    # Temporal Data
    last_updated: datetime = Field(default_factory=datetime.utcnow)
    confidence_score: float = Field(0.0, ge=0.0, le=1.0, description="Confidence in effectiveness data")
    
    class Config:
        use_enum_values = True
    
    def update_effectiveness(self, new_score: float) -> None:
        """Update effectiveness with new session data."""
        self.total_sessions += 1
        
        # Update average (running average)
        if self.total_sessions == 1:
            self.average_effectiveness = new_score
        else:
            # Weighted average favoring recent sessions
            weight = 0.3  # Weight for new score
            self.average_effectiveness = (
                (1 - weight) * self.average_effectiveness + 
                weight * new_score
            )
        
        # Update trend (keep last 10 scores)
        self.effectiveness_trend.append(new_score)
        if len(self.effectiveness_trend) > 10:
            self.effectiveness_trend.pop(0)
        
        # Update confidence based on sample size
        self.confidence_score = min(1.0, self.total_sessions / 20.0)
        self.last_updated = datetime.utcnow()


class TemporalStateManager:
    """
    Manages temporal learning optimization and pattern recognition.
    
    This class tracks learning effectiveness over time and provides
    recommendations for optimal constellation selection based on
    historical performance patterns.
    """
    
    def __init__(self, storage_path: str = "data/temporal_state"):
        """Initialize temporal state manager."""
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        # In-memory caches
        self.session_history: Dict[str, List[LearningSession]] = {}
        self.constellation_effectiveness: Dict[str, ConstellationEffectiveness] = {}
        self.user_patterns: Dict[str, Dict[str, Any]] = {}
        
        # Load existing data
        asyncio.create_task(self._load_state())
    
    async def _load_state(self) -> None:
        """Load temporal state from storage."""
        try:
            # Load constellation effectiveness data
            effectiveness_file = self.storage_path / "constellation_effectiveness.json"
            if effectiveness_file.exists():
                with open(effectiveness_file, 'r') as f:
                    data = json.load(f)
                    for key, item_data in data.items():
                        self.constellation_effectiveness[key] = ConstellationEffectiveness(**item_data)
            
            # Load user patterns
            patterns_file = self.storage_path / "user_patterns.json"
            if patterns_file.exists():
                with open(patterns_file, 'r') as f:
                    self.user_patterns = json.load(f)
        
        except Exception as e:
            # Log error but continue - start with empty state
            print(f"Warning: Could not load temporal state: {e}")
    
    async def _save_state(self) -> None:
        """Save temporal state to storage."""
        try:
            # Save constellation effectiveness
            effectiveness_file = self.storage_path / "constellation_effectiveness.json"
            effectiveness_data = {
                key: item.dict() for key, item in self.constellation_effectiveness.items()
            }
            with open(effectiveness_file, 'w') as f:
                json.dump(effectiveness_data, f, indent=2, default=str)
            
            # Save user patterns
            patterns_file = self.storage_path / "user_patterns.json"
            with open(patterns_file, 'w') as f:
                json.dump(self.user_patterns, f, indent=2, default=str)
        
        except Exception as e:
            print(f"Warning: Could not save temporal state: {e}")
    
    async def record_session(self, session: LearningSession) -> None:
        """Record a completed learning session."""
        
        # Add to session history
        user_id = session.user_id
        if user_id not in self.session_history:
            self.session_history[user_id] = []
        self.session_history[user_id].append(session)
        
        # Keep only recent sessions (last 100 per user)
        if len(self.session_history[user_id]) > 100:
            self.session_history[user_id] = self.session_history[user_id][-100:]
        
        # Update constellation effectiveness
        await self._update_constellation_effectiveness(session)
        
        # Update user patterns
        await self._update_user_patterns(session)
        
        # Save state periodically
        await self._save_state()
    
    async def _update_constellation_effectiveness(self, session: LearningSession) -> None:
        """Update constellation effectiveness tracking."""
        
        # Create context key
        context_key = f"{session.framework}_{session.user_skill_level}_{session.learning_style}"
        effectiveness_key = f"{session.constellation_type}_{context_key}"
        
        # Get or create effectiveness tracker
        if effectiveness_key not in self.constellation_effectiveness:
            self.constellation_effectiveness[effectiveness_key] = ConstellationEffectiveness(
                constellation_type=session.constellation_type,
                context_key=context_key,
            )
        
        # Update with session effectiveness
        effectiveness_score = session.metrics.calculate_overall_effectiveness()
        self.constellation_effectiveness[effectiveness_key].update_effectiveness(effectiveness_score)
    
    async def _update_user_patterns(self, session: LearningSession) -> None:
        """Update user learning patterns."""
        
        user_id = session.user_id
        if user_id not in self.user_patterns:
            self.user_patterns[user_id] = {
                "preferred_constellations": {},
                "learning_velocity": [],
                "engagement_patterns": {},
                "optimal_session_length": [],
            }
        
        patterns = self.user_patterns[user_id]
        
        # Update constellation preferences
        constellation_key = session.constellation_type.value if hasattr(session.constellation_type, 'value') else str(session.constellation_type)
        if constellation_key not in patterns["preferred_constellations"]:
            patterns["preferred_constellations"][constellation_key] = []
        patterns["preferred_constellations"][constellation_key].append(
            session.metrics.calculate_overall_effectiveness()
        )
        
        # Update learning velocity
        velocity = session.total_interactions / max(1, session.duration_minutes)
        patterns["learning_velocity"].append(velocity)
        if len(patterns["learning_velocity"]) > 20:
            patterns["learning_velocity"] = patterns["learning_velocity"][-20:]
        
        # Update optimal session length
        patterns["optimal_session_length"].append(session.duration_minutes)
        if len(patterns["optimal_session_length"]) > 20:
            patterns["optimal_session_length"] = patterns["optimal_session_length"][-20:]
    
    async def optimize_constellation_selection(
        self,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
        session_context: Dict[str, Any],
    ) -> Tuple[ConstellationType, float]:
        """
        Optimize constellation selection based on historical effectiveness.
        
        Returns:
            Tuple of (optimal_constellation_type, confidence_score)
        """
        
        # Create context key for matching
        context_key = f"{framework}_{user_profile.python_skill_level}_{user_profile.preferred_learning_style}"
        
        # Find effectiveness data for this context
        best_constellation = ConstellationType.THEORY_PRACTICE_BALANCED
        best_score = 0.0
        best_confidence = 0.0
        
        for effectiveness_key, effectiveness in self.constellation_effectiveness.items():
            if context_key in effectiveness_key:
                if effectiveness.average_effectiveness > best_score:
                    best_score = effectiveness.average_effectiveness
                    best_constellation = effectiveness.constellation_type
                    best_confidence = effectiveness.confidence_score
        
        # Apply user-specific patterns if available
        user_patterns = self.user_patterns.get(user_profile.user_id)
        if user_patterns and user_patterns["preferred_constellations"]:
            # Calculate user's historical preferences
            user_preferences = {}
            for constellation, scores in user_patterns["preferred_constellations"].items():
                if scores:
                    user_preferences[constellation] = sum(scores) / len(scores)
            
            # Find best user preference
            if user_preferences:
                best_user_constellation = max(user_preferences, key=user_preferences.get)
                best_user_score = user_preferences[best_user_constellation]
                
                # Combine global and user-specific data
                if best_user_score > best_score * 0.8:  # User preference threshold
                    try:
                        best_constellation = ConstellationType(best_user_constellation)
                        best_confidence = min(1.0, best_confidence + 0.2)  # Boost confidence
                    except ValueError:
                        pass  # Invalid constellation type, keep global best
        
        # Ensure minimum confidence
        if best_confidence < 0.3:
            best_confidence = 0.3
        
        return best_constellation, best_confidence
    
    def get_user_learning_analytics(self, user_id: str) -> Dict[str, Any]:
        """Get learning analytics for a specific user."""
        
        sessions = self.session_history.get(user_id, [])
        patterns = self.user_patterns.get(user_id, {})
        
        if not sessions:
            return {"message": "No learning history available"}
        
        # Calculate analytics
        total_sessions = len(sessions)
        total_learning_time = sum(s.duration_minutes for s in sessions)
        average_effectiveness = sum(
            s.metrics.calculate_overall_effectiveness() for s in sessions
        ) / total_sessions
        
        # Recent trend (last 10 sessions)
        recent_sessions = sessions[-10:]
        recent_effectiveness = sum(
            s.metrics.calculate_overall_effectiveness() for s in recent_sessions
        ) / len(recent_sessions)
        
        # Preferred constellation
        constellation_scores = {}
        for session in sessions:
            constellation = session.constellation_type.value
            if constellation not in constellation_scores:
                constellation_scores[constellation] = []
            constellation_scores[constellation].append(
                session.metrics.calculate_overall_effectiveness()
            )
        
        preferred_constellation = None
        if constellation_scores:
            constellation_averages = {
                k: sum(v) / len(v) for k, v in constellation_scores.items()
            }
            preferred_constellation = max(constellation_averages, key=constellation_averages.get)
        
        return {
            "total_sessions": total_sessions,
            "total_learning_time_minutes": total_learning_time,
            "average_effectiveness": round(average_effectiveness, 3),
            "recent_effectiveness": round(recent_effectiveness, 3),
            "effectiveness_trend": "improving" if recent_effectiveness > average_effectiveness else "stable",
            "preferred_constellation": preferred_constellation,
            "constellation_performance": {
                k: round(sum(v) / len(v), 3) for k, v in constellation_scores.items()
            },
            "learning_velocity": round(
                sum(patterns.get("learning_velocity", [0])) / max(1, len(patterns.get("learning_velocity", []))), 2
            ),
            "optimal_session_length": round(
                sum(patterns.get("optimal_session_length", [30])) / max(1, len(patterns.get("optimal_session_length", []))), 1
            ),
        }
    
    async def cleanup_old_data(self, days_to_keep: int = 90) -> int:
        """Clean up old session data."""
        cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)
        cleanup_count = 0
        
        for user_id, sessions in self.session_history.items():
            original_count = len(sessions)
            self.session_history[user_id] = [
                s for s in sessions if s.start_time > cutoff_date
            ]
            cleanup_count += original_count - len(self.session_history[user_id])
        
        await self._save_state()
        return cleanup_count
