"""
User profile management for personalized learning experiences.

This module defines user profile structures and utilities for managing
learner characteristics, preferences, and progress tracking.
"""

from enum import Enum
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator


class SkillLevel(str, Enum):
    """Programming skill level enumeration."""
    NONE = "none"
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"


class LearningPace(str, Enum):
    """Learning pace preference enumeration."""
    SLOW = "slow"
    MODERATE = "moderate"
    FAST = "fast"
    INTENSIVE = "intensive"


class LearningStyle(str, Enum):
    """Learning style preference enumeration."""
    VISUAL = "visual"
    HANDS_ON = "hands_on"
    THEORETICAL = "theoretical"
    MIXED = "mixed"


class DifficultyProgression(str, Enum):
    """Difficulty progression preference enumeration."""
    GRADUAL = "gradual"
    MODERATE = "moderate"
    AGGRESSIVE = "aggressive"


class FeedbackStyle(str, Enum):
    """Feedback style preference enumeration."""
    ENCOURAGING = "encouraging"
    DIRECT = "direct"
    DETAILED = "detailed"
    MINIMAL = "minimal"


class UserProfile(BaseModel):
    """
    Comprehensive user profile for personalized learning.
    
    This class captures all relevant user characteristics needed
    for adaptive constellation formation and temporal optimization.
    """
    
    # Basic Information
    user_id: str = Field(..., description="Unique user identifier")
    name: Optional[str] = Field(None, description="User's display name")
    email: Optional[str] = Field(None, description="User's email address")
    
    # Experience and Skills
    programming_experience_years: int = Field(
        0, ge=0, le=50, description="Years of programming experience"
    )
    python_skill_level: SkillLevel = Field(
        SkillLevel.BEGINNER, description="Python programming skill level"
    )
    ai_ml_experience: SkillLevel = Field(
        SkillLevel.NONE, description="AI/ML experience level"
    )
    
    # Learning Preferences
    learning_pace: LearningPace = Field(
        LearningPace.MODERATE, description="Preferred learning pace"
    )
    preferred_learning_style: LearningStyle = Field(
        LearningStyle.MIXED, description="Preferred learning style"
    )
    difficulty_progression: DifficultyProgression = Field(
        DifficultyProgression.MODERATE, description="Difficulty progression preference"
    )
    feedback_style: FeedbackStyle = Field(
        FeedbackStyle.ENCOURAGING, description="Preferred feedback style"
    )
    
    # Goals and Interests
    learning_goals: List[str] = Field(
        default_factory=list, description="Specific learning objectives"
    )
    interested_frameworks: List[str] = Field(
        default_factory=list, description="Frameworks of interest"
    )
    project_interests: List[str] = Field(
        default_factory=list, description="Types of projects to build"
    )
    
    # Session Preferences
    preferred_session_length_minutes: int = Field(
        30, ge=10, le=180, description="Preferred session length in minutes"
    )
    availability_hours: List[int] = Field(
        default_factory=lambda: list(range(9, 17)), 
        description="Available hours (24-hour format)"
    )
    timezone: str = Field("UTC", description="User's timezone")
    
    # Progress Tracking
    total_learning_hours: float = Field(0.0, ge=0, description="Total learning hours")
    completed_modules: List[str] = Field(
        default_factory=list, description="Completed learning modules"
    )
    current_streak_days: int = Field(0, ge=0, description="Current learning streak")
    longest_streak_days: int = Field(0, ge=0, description="Longest learning streak")
    
    # Temporal Optimization Data
    optimal_constellation_history: Dict[str, Any] = Field(
        default_factory=dict, description="Historical constellation effectiveness"
    )
    learning_pattern_data: Dict[str, Any] = Field(
        default_factory=dict, description="Learned user patterns"
    )
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    last_active_at: Optional[datetime] = Field(None)
    
    @validator('email')
    def validate_email(cls, v):
        """Validate email format if provided."""
        if v is not None and '@' not in v:
            raise ValueError('Invalid email format')
        return v
    
    @validator('availability_hours')
    def validate_availability_hours(cls, v):
        """Validate availability hours are within 0-23 range."""
        if not all(0 <= hour <= 23 for hour in v):
            raise ValueError('Availability hours must be between 0 and 23')
        return v
    
    def update_activity(self) -> None:
        """Update last activity timestamp."""
        self.last_active_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
    
    def add_learning_hours(self, hours: float) -> None:
        """Add learning hours to total."""
        self.total_learning_hours += hours
        self.update_activity()
    
    def complete_module(self, module_id: str) -> None:
        """Mark a module as completed."""
        if module_id not in self.completed_modules:
            self.completed_modules.append(module_id)
            self.update_activity()
    
    def update_streak(self, active_today: bool = True) -> None:
        """Update learning streak counters."""
        if active_today:
            self.current_streak_days += 1
            if self.current_streak_days > self.longest_streak_days:
                self.longest_streak_days = self.current_streak_days
        else:
            self.current_streak_days = 0
        self.update_activity()
    
    def get_experience_level(self) -> str:
        """Get overall experience level description."""
        if self.programming_experience_years == 0:
            return "Complete Beginner"
        elif self.programming_experience_years < 2:
            return "Novice"
        elif self.programming_experience_years < 5:
            return "Intermediate"
        elif self.programming_experience_years < 10:
            return "Advanced"
        else:
            return "Expert"
    
    def is_suitable_for_advanced_topics(self) -> bool:
        """Check if user is ready for advanced topics."""
        return (
            self.python_skill_level in [SkillLevel.ADVANCED, SkillLevel.EXPERT] and
            self.programming_experience_years >= 3
        )
    
    def get_recommended_pace_multiplier(self) -> float:
        """Get pace multiplier for content delivery."""
        pace_multipliers = {
            LearningPace.SLOW: 0.7,
            LearningPace.MODERATE: 1.0,
            LearningPace.FAST: 1.3,
            LearningPace.INTENSIVE: 1.6,
        }
        return pace_multipliers[self.learning_pace]
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        validate_assignment = True
        arbitrary_types_allowed = True
