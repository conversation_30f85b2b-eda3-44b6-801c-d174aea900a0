"""
Constellation configuration management for adaptive agent teams.

This module defines the 8 specialized constellation types and their
agent compositions for different learning scenarios.
"""

from enum import Enum
from typing import List, Dict, Any, Set
from pydantic import BaseModel, Field


class ConstellationType(str, Enum):
    """Enumeration of available constellation types."""
    KNOWLEDGE_INTENSIVE = "knowledge_intensive"
    HANDS_ON_FOCUSED = "hands_on_focused"
    THEORY_PRACTICE_BALANCED = "theory_practice_balanced"
    RESEARCH_INTENSIVE = "research_intensive"
    QUICK_LEARNING = "quick_learning"
    DEEP_EXPLORATION = "deep_exploration"
    PROJECT_ORIENTED = "project_oriented"
    ASSESSMENT_FOCUSED = "assessment_focused"


class AgentRole(str, Enum):
    """Enumeration of available agent roles."""
    # Knowledge Domain
    INSTRUCTOR = "instructor"
    DOCUMENTATION_EXPERT = "documentation_expert"
    RESEARCH_ASSISTANT = "research_assistant"
    KNOWLEDGE_SYNTHESIZER = "knowledge_synthesizer"
    
    # Practice Domain
    CODE_ASSISTANT = "code_assistant"
    PRACTICE_FACILITATOR = "practice_facilitator"
    PROJECT_GUIDE = "project_guide"
    TROUBLESHOOTER = "troubleshooter"
    
    # Support Domain
    MENTOR = "mentor"
    MOTIVATIONAL_COACH = "motivational_coach"
    
    # Assessment Domain
    ASSESSMENT_AGENT = "assessment_agent"
    PROGRESS_TRACKER = "progress_tracker"


class AgentDomain(str, Enum):
    """Agent domain classifications."""
    KNOWLEDGE = "knowledge"
    PRACTICE = "practice"
    SUPPORT = "support"
    ASSESSMENT = "assessment"


class ConstellationConfig(BaseModel):
    """Configuration for a specific constellation type."""
    
    constellation_type: ConstellationType = Field(..., description="Constellation type identifier")
    display_name: str = Field(..., description="Human-readable constellation name")
    description: str = Field(..., description="Constellation description")
    focus_area: str = Field(..., description="Primary focus area")
    
    # Agent Configuration
    primary_agents: List[AgentRole] = Field(..., description="Primary agents in constellation")
    support_agents: List[AgentRole] = Field(default_factory=list, description="Support agents")
    max_concurrent_agents: int = Field(3, ge=1, le=8, description="Maximum concurrent agents")
    
    # Learning Characteristics
    best_for_learning_styles: List[str] = Field(default_factory=list, description="Optimal learning styles")
    best_for_skill_levels: List[str] = Field(default_factory=list, description="Optimal skill levels")
    best_for_paces: List[str] = Field(default_factory=list, description="Optimal learning paces")
    
    # Effectiveness Metrics
    theoretical_weight: float = Field(0.5, ge=0.0, le=1.0, description="Theoretical content weight")
    practical_weight: float = Field(0.5, ge=0.0, le=1.0, description="Practical content weight")
    interaction_intensity: float = Field(0.5, ge=0.0, le=1.0, description="Interaction intensity level")
    
    # Handoff Configuration
    handoff_triggers: Dict[str, List[AgentRole]] = Field(
        default_factory=dict, description="Content-based handoff triggers"
    )
    
    class Config:
        use_enum_values = True


# Constellation Configurations
CONSTELLATION_CONFIGS: Dict[ConstellationType, ConstellationConfig] = {
    ConstellationType.KNOWLEDGE_INTENSIVE: ConstellationConfig(
        constellation_type=ConstellationType.KNOWLEDGE_INTENSIVE,
        display_name="Knowledge Intensive",
        description="Focus on theoretical understanding and conceptual learning",
        focus_area="Theoretical foundations and deep understanding",
        primary_agents=[
            AgentRole.INSTRUCTOR,
            AgentRole.DOCUMENTATION_EXPERT,
            AgentRole.KNOWLEDGE_SYNTHESIZER,
        ],
        support_agents=[AgentRole.RESEARCH_ASSISTANT],
        max_concurrent_agents=4,
        best_for_learning_styles=["theoretical", "visual"],
        best_for_skill_levels=["beginner", "intermediate"],
        best_for_paces=["slow", "moderate"],
        theoretical_weight=0.8,
        practical_weight=0.2,
        interaction_intensity=0.6,
        handoff_triggers={
            "explanation": [AgentRole.INSTRUCTOR],
            "documentation": [AgentRole.DOCUMENTATION_EXPERT],
            "concept": [AgentRole.KNOWLEDGE_SYNTHESIZER],
            "research": [AgentRole.RESEARCH_ASSISTANT],
        },
    ),
    
    ConstellationType.HANDS_ON_FOCUSED: ConstellationConfig(
        constellation_type=ConstellationType.HANDS_ON_FOCUSED,
        display_name="Hands-On Focused",
        description="Emphasis on practical implementation and learning by doing",
        focus_area="Practical skills and implementation",
        primary_agents=[
            AgentRole.CODE_ASSISTANT,
            AgentRole.PRACTICE_FACILITATOR,
            AgentRole.PROJECT_GUIDE,
        ],
        support_agents=[AgentRole.TROUBLESHOOTER],
        max_concurrent_agents=4,
        best_for_learning_styles=["hands_on", "mixed"],
        best_for_skill_levels=["intermediate", "advanced"],
        best_for_paces=["fast", "intensive"],
        theoretical_weight=0.2,
        practical_weight=0.8,
        interaction_intensity=0.9,
        handoff_triggers={
            "code": [AgentRole.CODE_ASSISTANT],
            "example": [AgentRole.CODE_ASSISTANT],
            "practice": [AgentRole.PRACTICE_FACILITATOR],
            "project": [AgentRole.PROJECT_GUIDE],
            "error": [AgentRole.TROUBLESHOOTER],
            "debug": [AgentRole.TROUBLESHOOTER],
        },
    ),
    
    ConstellationType.THEORY_PRACTICE_BALANCED: ConstellationConfig(
        constellation_type=ConstellationType.THEORY_PRACTICE_BALANCED,
        display_name="Theory-Practice Balanced",
        description="Balanced approach combining theoretical understanding with practical application",
        focus_area="Comprehensive understanding through balanced learning",
        primary_agents=[
            AgentRole.INSTRUCTOR,
            AgentRole.CODE_ASSISTANT,
            AgentRole.MENTOR,
        ],
        support_agents=[AgentRole.PRACTICE_FACILITATOR],
        max_concurrent_agents=4,
        best_for_learning_styles=["mixed", "hands_on"],
        best_for_skill_levels=["beginner", "intermediate", "advanced"],
        best_for_paces=["moderate", "fast"],
        theoretical_weight=0.5,
        practical_weight=0.5,
        interaction_intensity=0.7,
        handoff_triggers={
            "explanation": [AgentRole.INSTRUCTOR],
            "code": [AgentRole.CODE_ASSISTANT],
            "guidance": [AgentRole.MENTOR],
            "practice": [AgentRole.PRACTICE_FACILITATOR],
        },
    ),
    
    ConstellationType.RESEARCH_INTENSIVE: ConstellationConfig(
        constellation_type=ConstellationType.RESEARCH_INTENSIVE,
        display_name="Research Intensive",
        description="Deep exploration of advanced topics and cutting-edge features",
        focus_area="Advanced research and exploration",
        primary_agents=[
            AgentRole.RESEARCH_ASSISTANT,
            AgentRole.DOCUMENTATION_EXPERT,
            AgentRole.KNOWLEDGE_SYNTHESIZER,
        ],
        support_agents=[AgentRole.INSTRUCTOR],
        max_concurrent_agents=4,
        best_for_learning_styles=["theoretical", "mixed"],
        best_for_skill_levels=["advanced", "expert"],
        best_for_paces=["slow", "moderate"],
        theoretical_weight=0.7,
        practical_weight=0.3,
        interaction_intensity=0.5,
        handoff_triggers={
            "research": [AgentRole.RESEARCH_ASSISTANT],
            "documentation": [AgentRole.DOCUMENTATION_EXPERT],
            "synthesis": [AgentRole.KNOWLEDGE_SYNTHESIZER],
            "advanced": [AgentRole.RESEARCH_ASSISTANT],
        },
    ),
    
    ConstellationType.QUICK_LEARNING: ConstellationConfig(
        constellation_type=ConstellationType.QUICK_LEARNING,
        display_name="Quick Learning",
        description="Rapid skill acquisition for time-constrained learning",
        focus_area="Efficient and focused learning",
        primary_agents=[
            AgentRole.INSTRUCTOR,
            AgentRole.CODE_ASSISTANT,
            AgentRole.PROGRESS_TRACKER,
        ],
        support_agents=[],
        max_concurrent_agents=3,
        best_for_learning_styles=["hands_on", "mixed"],
        best_for_skill_levels=["intermediate", "advanced"],
        best_for_paces=["fast", "intensive"],
        theoretical_weight=0.3,
        practical_weight=0.7,
        interaction_intensity=0.8,
        handoff_triggers={
            "quick": [AgentRole.INSTRUCTOR],
            "code": [AgentRole.CODE_ASSISTANT],
            "progress": [AgentRole.PROGRESS_TRACKER],
        },
    ),
    
    ConstellationType.DEEP_EXPLORATION: ConstellationConfig(
        constellation_type=ConstellationType.DEEP_EXPLORATION,
        display_name="Deep Exploration",
        description="Thorough understanding and mastery-oriented learning",
        focus_area="Deep mastery and comprehensive understanding",
        primary_agents=[
            AgentRole.RESEARCH_ASSISTANT,
            AgentRole.MENTOR,
            AgentRole.KNOWLEDGE_SYNTHESIZER,
        ],
        support_agents=[AgentRole.DOCUMENTATION_EXPERT],
        max_concurrent_agents=4,
        best_for_learning_styles=["theoretical", "mixed"],
        best_for_skill_levels=["intermediate", "advanced", "expert"],
        best_for_paces=["slow", "moderate"],
        theoretical_weight=0.6,
        practical_weight=0.4,
        interaction_intensity=0.6,
        handoff_triggers={
            "deep": [AgentRole.RESEARCH_ASSISTANT],
            "mastery": [AgentRole.MENTOR],
            "synthesis": [AgentRole.KNOWLEDGE_SYNTHESIZER],
        },
    ),
    
    ConstellationType.PROJECT_ORIENTED: ConstellationConfig(
        constellation_type=ConstellationType.PROJECT_ORIENTED,
        display_name="Project Oriented",
        description="Real-world application building and project development",
        focus_area="Building actual projects and applications",
        primary_agents=[
            AgentRole.PROJECT_GUIDE,
            AgentRole.CODE_ASSISTANT,
            AgentRole.TROUBLESHOOTER,
        ],
        support_agents=[AgentRole.MENTOR],
        max_concurrent_agents=4,
        best_for_learning_styles=["hands_on", "mixed"],
        best_for_skill_levels=["intermediate", "advanced"],
        best_for_paces=["moderate", "fast"],
        theoretical_weight=0.2,
        practical_weight=0.8,
        interaction_intensity=0.9,
        handoff_triggers={
            "project": [AgentRole.PROJECT_GUIDE],
            "build": [AgentRole.PROJECT_GUIDE],
            "code": [AgentRole.CODE_ASSISTANT],
            "error": [AgentRole.TROUBLESHOOTER],
            "guidance": [AgentRole.MENTOR],
        },
    ),
    
    ConstellationType.ASSESSMENT_FOCUSED: ConstellationConfig(
        constellation_type=ConstellationType.ASSESSMENT_FOCUSED,
        display_name="Assessment Focused",
        description="Progress evaluation and certification preparation",
        focus_area="Assessment and progress evaluation",
        primary_agents=[
            AgentRole.ASSESSMENT_AGENT,
            AgentRole.PROGRESS_TRACKER,
            AgentRole.MOTIVATIONAL_COACH,
        ],
        support_agents=[AgentRole.INSTRUCTOR],
        max_concurrent_agents=4,
        best_for_learning_styles=["mixed", "theoretical"],
        best_for_skill_levels=["intermediate", "advanced"],
        best_for_paces=["moderate", "fast"],
        theoretical_weight=0.4,
        practical_weight=0.6,
        interaction_intensity=0.7,
        handoff_triggers={
            "assessment": [AgentRole.ASSESSMENT_AGENT],
            "test": [AgentRole.ASSESSMENT_AGENT],
            "progress": [AgentRole.PROGRESS_TRACKER],
            "motivation": [AgentRole.MOTIVATIONAL_COACH],
        },
    ),
}


def get_constellation_config(constellation_type: ConstellationType) -> ConstellationConfig:
    """Get configuration for a specific constellation type."""
    return CONSTELLATION_CONFIGS[constellation_type]


def get_available_constellations() -> List[ConstellationType]:
    """Get list of available constellation types."""
    return list(CONSTELLATION_CONFIGS.keys())


def get_agents_by_domain(domain: AgentDomain) -> List[AgentRole]:
    """Get all agents belonging to a specific domain."""
    domain_agents = {
        AgentDomain.KNOWLEDGE: [
            AgentRole.INSTRUCTOR,
            AgentRole.DOCUMENTATION_EXPERT,
            AgentRole.RESEARCH_ASSISTANT,
            AgentRole.KNOWLEDGE_SYNTHESIZER,
        ],
        AgentDomain.PRACTICE: [
            AgentRole.CODE_ASSISTANT,
            AgentRole.PRACTICE_FACILITATOR,
            AgentRole.PROJECT_GUIDE,
            AgentRole.TROUBLESHOOTER,
        ],
        AgentDomain.SUPPORT: [
            AgentRole.MENTOR,
            AgentRole.MOTIVATIONAL_COACH,
        ],
        AgentDomain.ASSESSMENT: [
            AgentRole.ASSESSMENT_AGENT,
            AgentRole.PROGRESS_TRACKER,
        ],
    }
    return domain_agents.get(domain, [])


def get_optimal_constellation_for_profile(
    learning_style: str,
    skill_level: str,
    learning_pace: str
) -> List[ConstellationType]:
    """Get optimal constellation types for a user profile."""
    optimal_constellations = []
    
    for constellation_type, config in CONSTELLATION_CONFIGS.items():
        score = 0
        
        if learning_style in config.best_for_learning_styles:
            score += 3
        if skill_level in config.best_for_skill_levels:
            score += 3
        if learning_pace in config.best_for_paces:
            score += 2
            
        if score >= 5:  # High match threshold
            optimal_constellations.append(constellation_type)
    
    return optimal_constellations or [ConstellationType.THEORY_PRACTICE_BALANCED]
