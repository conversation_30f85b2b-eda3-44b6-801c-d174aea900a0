"""
User memory management for persistent learning state.

This module provides user-specific memory capabilities for maintaining
long-term learning progress, preferences, and patterns.
"""

import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from pathlib import Path
from pydantic import BaseModel, Field

from ..config.user_profiles import UserProfile
from ..config.constellation_configs import ConstellationType


class UserMemory(BaseModel):
    """
    Manages persistent memory for individual users.
    
    Tracks learning progress, preferences, and behavioral patterns
    across multiple sessions and time periods.
    """
    
    user_id: str = Field(..., description="User identifier")
    
    # Learning History
    session_history: List[str] = Field(
        default_factory=list, description="List of session IDs"
    )
    total_learning_time_minutes: float = Field(
        0.0, description="Total learning time across all sessions"
    )
    
    # Progress Tracking
    framework_progress: Dict[str, Dict[str, Any]] = Field(
        default_factory=dict, description="Progress by framework"
    )
    completed_modules: List[str] = Field(
        default_factory=list, description="Completed learning modules"
    )
    current_learning_goals: List[str] = Field(
        default_factory=list, description="Current learning goals"
    )
    
    # Behavioral Patterns
    preferred_constellations: Dict[str, float] = Field(
        default_factory=dict, description="Constellation effectiveness scores"
    )
    optimal_session_length_minutes: float = Field(
        30.0, description="Optimal session length based on history"
    )
    learning_velocity_patterns: List[float] = Field(
        default_factory=list, description="Learning velocity over time"
    )
    
    # Engagement Patterns
    best_learning_times: List[int] = Field(
        default_factory=list, description="Best learning hours (0-23)"
    )
    consistency_score: float = Field(
        0.5, description="Learning consistency score"
    )
    motivation_level: float = Field(
        0.7, description="Current motivation level"
    )
    
    # Adaptive Learning Data
    difficulty_preferences: Dict[str, float] = Field(
        default_factory=dict, description="Difficulty level preferences"
    )
    learning_style_effectiveness: Dict[str, float] = Field(
        default_factory=dict, description="Learning style effectiveness"
    )
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    last_updated: datetime = Field(default_factory=datetime.utcnow)
    last_session_at: Optional[datetime] = Field(None, description="Last session timestamp")
    
    def record_session_completion(
        self,
        session_id: str,
        duration_minutes: float,
        constellation_type: ConstellationType,
        effectiveness_score: float,
        framework: str,
        modules_completed: List[str] = None
    ) -> None:
        """Record completion of a learning session."""
        
        # Update session history
        self.session_history.append(session_id)
        if len(self.session_history) > 100:  # Keep last 100 sessions
            self.session_history.pop(0)
        
        # Update learning time
        self.total_learning_time_minutes += duration_minutes
        
        # Update constellation preferences
        constellation_key = constellation_type.value if hasattr(constellation_type, 'value') else str(constellation_type)
        if constellation_key not in self.preferred_constellations:
            self.preferred_constellations[constellation_key] = effectiveness_score
        else:
            # Weighted average favoring recent sessions
            current_score = self.preferred_constellations[constellation_key]
            self.preferred_constellations[constellation_key] = (
                0.7 * current_score + 0.3 * effectiveness_score
            )
        
        # Update framework progress
        if framework not in self.framework_progress:
            self.framework_progress[framework] = {
                "sessions": 0,
                "total_time": 0.0,
                "average_effectiveness": 0.0,
                "modules_completed": []
            }
        
        progress = self.framework_progress[framework]
        progress["sessions"] += 1
        progress["total_time"] += duration_minutes
        
        # Update average effectiveness
        if progress["sessions"] == 1:
            progress["average_effectiveness"] = effectiveness_score
        else:
            current_avg = progress["average_effectiveness"]
            progress["average_effectiveness"] = (
                (current_avg * (progress["sessions"] - 1) + effectiveness_score) / 
                progress["sessions"]
            )
        
        # Update completed modules
        if modules_completed:
            for module in modules_completed:
                if module not in self.completed_modules:
                    self.completed_modules.append(module)
                if module not in progress["modules_completed"]:
                    progress["modules_completed"].append(module)
        
        # Update learning velocity
        velocity = 1.0 / max(1, duration_minutes / 30)  # Normalize to 30-min sessions
        self.learning_velocity_patterns.append(velocity)
        if len(self.learning_velocity_patterns) > 20:
            self.learning_velocity_patterns.pop(0)
        
        # Update optimal session length
        self._update_optimal_session_length(duration_minutes, effectiveness_score)
        
        # Update timestamps
        self.last_session_at = datetime.utcnow()
        self.last_updated = datetime.utcnow()
    
    def _update_optimal_session_length(
        self,
        duration_minutes: float,
        effectiveness_score: float
    ) -> None:
        """Update optimal session length based on effectiveness."""
        
        # If this session was highly effective, adjust optimal length toward it
        if effectiveness_score > 0.8:
            weight = 0.2
            self.optimal_session_length_minutes = (
                (1 - weight) * self.optimal_session_length_minutes + 
                weight * duration_minutes
            )
        
        # Keep within reasonable bounds
        self.optimal_session_length_minutes = max(15, min(90, self.optimal_session_length_minutes))
    
    def get_preferred_constellation(self) -> Optional[ConstellationType]:
        """Get the most preferred constellation type."""
        
        if not self.preferred_constellations:
            return None
        
        best_constellation = max(
            self.preferred_constellations.items(),
            key=lambda x: x[1]
        )
        
        try:
            return ConstellationType(best_constellation[0])
        except ValueError:
            return None
    
    def get_learning_streak(self) -> int:
        """Calculate current learning streak in days."""
        
        if not self.last_session_at:
            return 0
        
        # Simple streak calculation - sessions within last 7 days
        days_since_last = (datetime.utcnow() - self.last_session_at).days
        
        if days_since_last <= 1:
            return 1  # Active streak
        else:
            return 0  # Streak broken
    
    def get_framework_mastery(self, framework: str) -> Dict[str, Any]:
        """Get mastery information for a specific framework."""
        
        if framework not in self.framework_progress:
            return {
                "mastery_level": "beginner",
                "completion_percentage": 0.0,
                "time_invested": 0.0,
                "effectiveness": 0.0
            }
        
        progress = self.framework_progress[framework]
        
        # Estimate mastery level based on time and effectiveness
        time_hours = progress["total_time"] / 60
        effectiveness = progress["average_effectiveness"]
        
        if time_hours < 5 or effectiveness < 0.5:
            mastery_level = "beginner"
        elif time_hours < 15 or effectiveness < 0.7:
            mastery_level = "intermediate"
        elif time_hours < 30 or effectiveness < 0.85:
            mastery_level = "advanced"
        else:
            mastery_level = "expert"
        
        # Estimate completion percentage (rough approximation)
        modules_completed = len(progress["modules_completed"])
        estimated_total_modules = 10  # Rough estimate
        completion_percentage = min(100, (modules_completed / estimated_total_modules) * 100)
        
        return {
            "mastery_level": mastery_level,
            "completion_percentage": completion_percentage,
            "time_invested": time_hours,
            "effectiveness": effectiveness,
            "sessions": progress["sessions"],
            "modules_completed": modules_completed
        }
    
    def get_learning_analytics(self) -> Dict[str, Any]:
        """Get comprehensive learning analytics."""
        
        total_sessions = len(self.session_history)
        total_hours = self.total_learning_time_minutes / 60
        
        # Calculate average session length
        avg_session_length = (
            self.total_learning_time_minutes / max(1, total_sessions)
        )
        
        # Get framework summaries
        framework_summaries = {}
        for framework in self.framework_progress:
            framework_summaries[framework] = self.get_framework_mastery(framework)
        
        # Calculate learning velocity trend
        velocity_trend = "stable"
        if len(self.learning_velocity_patterns) >= 5:
            recent_avg = sum(self.learning_velocity_patterns[-5:]) / 5
            older_avg = sum(self.learning_velocity_patterns[:-5]) / max(1, len(self.learning_velocity_patterns) - 5)
            
            if recent_avg > older_avg * 1.1:
                velocity_trend = "improving"
            elif recent_avg < older_avg * 0.9:
                velocity_trend = "declining"
        
        return {
            "overview": {
                "total_sessions": total_sessions,
                "total_learning_hours": round(total_hours, 1),
                "average_session_length": round(avg_session_length, 1),
                "learning_streak": self.get_learning_streak(),
                "consistency_score": round(self.consistency_score, 2),
            },
            "preferences": {
                "preferred_constellation": self.get_preferred_constellation().value if self.get_preferred_constellation() else None,
                "optimal_session_length": round(self.optimal_session_length_minutes, 1),
                "constellation_scores": self.preferred_constellations,
            },
            "progress": {
                "frameworks": framework_summaries,
                "total_modules_completed": len(self.completed_modules),
                "current_goals": self.current_learning_goals,
            },
            "patterns": {
                "learning_velocity_trend": velocity_trend,
                "motivation_level": round(self.motivation_level, 2),
                "best_learning_times": self.best_learning_times,
            },
            "timestamps": {
                "account_created": self.created_at.isoformat(),
                "last_session": self.last_session_at.isoformat() if self.last_session_at else None,
                "last_updated": self.last_updated.isoformat(),
            }
        }
    
    def update_learning_goals(self, goals: List[str]) -> None:
        """Update current learning goals."""
        self.current_learning_goals = goals
        self.last_updated = datetime.utcnow()
    
    def save_to_file(self, file_path: Path) -> None:
        """Save user memory to file."""
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path, 'w') as f:
            json.dump(self.dict(), f, indent=2, default=str)
    
    @classmethod
    def load_from_file(cls, file_path: Path) -> Optional['UserMemory']:
        """Load user memory from file."""
        if not file_path.exists():
            return None
        
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            # Convert datetime strings back to datetime objects
            if 'created_at' in data:
                data['created_at'] = datetime.fromisoformat(data['created_at'])
            if 'last_updated' in data:
                data['last_updated'] = datetime.fromisoformat(data['last_updated'])
            if 'last_session_at' in data and data['last_session_at']:
                data['last_session_at'] = datetime.fromisoformat(data['last_session_at'])
            
            return cls(**data)
            
        except Exception as e:
            print(f"Error loading user memory: {e}")
            return None
    
    class Config:
        arbitrary_types_allowed = True
