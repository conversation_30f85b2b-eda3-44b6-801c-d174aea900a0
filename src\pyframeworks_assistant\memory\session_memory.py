"""
Session memory management for GAAPF learning sessions.

This module provides session-specific memory capabilities for maintaining
context and state during learning interactions.
"""

import json
from typing import Dict, Any, List, Optional
from datetime import datetime
from pathlib import Path
from pydantic import BaseModel, Field


class SessionMemory(BaseModel):
    """
    Manages memory for individual learning sessions.
    
    Provides context retention, conversation history, and
    session-specific state management.
    """
    
    session_id: str = Field(..., description="Unique session identifier")
    user_id: str = Field(..., description="User identifier")
    
    # Conversation History
    conversation_history: List[Dict[str, Any]] = Field(
        default_factory=list, description="Complete conversation history"
    )
    
    # Context Data
    session_context: Dict[str, Any] = Field(
        default_factory=dict, description="Session-specific context"
    )
    
    # Learning State
    current_topic: Optional[str] = Field(None, description="Current learning topic")
    learning_objectives: List[str] = Field(
        default_factory=list, description="Session learning objectives"
    )
    completed_objectives: List[str] = Field(
        default_factory=list, description="Completed objectives"
    )
    
    # Agent State
    active_agents: List[str] = Field(
        default_factory=list, description="Currently active agents"
    )
    agent_handoffs: List[Dict[str, Any]] = Field(
        default_factory=list, description="Agent handoff history"
    )
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    last_updated: datetime = Field(default_factory=datetime.utcnow)
    
    def add_conversation_turn(
        self,
        user_message: str,
        agent_response: str,
        agent_role: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Add a conversation turn to history."""
        
        turn = {
            "timestamp": datetime.utcnow().isoformat(),
            "user_message": user_message,
            "agent_response": agent_response,
            "agent_role": agent_role,
            "metadata": metadata or {}
        }
        
        self.conversation_history.append(turn)
        self.last_updated = datetime.utcnow()
    
    def update_context(self, key: str, value: Any) -> None:
        """Update session context."""
        self.session_context[key] = value
        self.last_updated = datetime.utcnow()
    
    def get_context(self, key: str, default: Any = None) -> Any:
        """Get value from session context."""
        return self.session_context.get(key, default)
    
    def add_learning_objective(self, objective: str) -> None:
        """Add a learning objective."""
        if objective not in self.learning_objectives:
            self.learning_objectives.append(objective)
            self.last_updated = datetime.utcnow()
    
    def complete_objective(self, objective: str) -> bool:
        """Mark an objective as completed."""
        if objective in self.learning_objectives and objective not in self.completed_objectives:
            self.completed_objectives.append(objective)
            self.last_updated = datetime.utcnow()
            return True
        return False
    
    def record_agent_handoff(
        self,
        from_agent: str,
        to_agent: str,
        reason: str,
        confidence: float
    ) -> None:
        """Record an agent handoff."""
        
        handoff = {
            "timestamp": datetime.utcnow().isoformat(),
            "from_agent": from_agent,
            "to_agent": to_agent,
            "reason": reason,
            "confidence": confidence
        }
        
        self.agent_handoffs.append(handoff)
        
        # Update active agents
        if to_agent not in self.active_agents:
            self.active_agents.append(to_agent)
        
        self.last_updated = datetime.utcnow()
    
    def get_recent_conversation(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get recent conversation history."""
        return self.conversation_history[-limit:] if self.conversation_history else []
    
    def get_conversation_summary(self) -> Dict[str, Any]:
        """Get a summary of the conversation."""
        
        total_turns = len(self.conversation_history)
        if total_turns == 0:
            return {"message": "No conversation history"}
        
        # Count agent interactions
        agent_counts = {}
        for turn in self.conversation_history:
            agent = turn.get("agent_role", "unknown")
            agent_counts[agent] = agent_counts.get(agent, 0) + 1
        
        # Calculate session duration
        if self.conversation_history:
            first_turn = datetime.fromisoformat(self.conversation_history[0]["timestamp"])
            last_turn = datetime.fromisoformat(self.conversation_history[-1]["timestamp"])
            duration_minutes = (last_turn - first_turn).total_seconds() / 60
        else:
            duration_minutes = 0
        
        return {
            "total_turns": total_turns,
            "duration_minutes": round(duration_minutes, 1),
            "agent_interactions": agent_counts,
            "handoffs": len(self.agent_handoffs),
            "objectives_completed": len(self.completed_objectives),
            "objectives_total": len(self.learning_objectives),
            "current_topic": self.current_topic,
        }
    
    def clear_history(self, keep_recent: int = 0) -> None:
        """Clear conversation history, optionally keeping recent turns."""
        if keep_recent > 0:
            self.conversation_history = self.conversation_history[-keep_recent:]
        else:
            self.conversation_history.clear()
        
        self.last_updated = datetime.utcnow()
    
    def export_session_data(self) -> Dict[str, Any]:
        """Export complete session data."""
        return {
            "session_info": {
                "session_id": self.session_id,
                "user_id": self.user_id,
                "created_at": self.created_at.isoformat(),
                "last_updated": self.last_updated.isoformat(),
            },
            "conversation_history": self.conversation_history,
            "session_context": self.session_context,
            "learning_state": {
                "current_topic": self.current_topic,
                "objectives": self.learning_objectives,
                "completed_objectives": self.completed_objectives,
            },
            "agent_state": {
                "active_agents": self.active_agents,
                "handoffs": self.agent_handoffs,
            },
            "summary": self.get_conversation_summary(),
        }
    
    def save_to_file(self, file_path: Path) -> None:
        """Save session memory to file."""
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path, 'w') as f:
            json.dump(self.export_session_data(), f, indent=2, default=str)
    
    @classmethod
    def load_from_file(cls, file_path: Path) -> Optional['SessionMemory']:
        """Load session memory from file."""
        if not file_path.exists():
            return None
        
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            session_info = data["session_info"]
            
            memory = cls(
                session_id=session_info["session_id"],
                user_id=session_info["user_id"],
                created_at=datetime.fromisoformat(session_info["created_at"]),
                last_updated=datetime.fromisoformat(session_info["last_updated"]),
            )
            
            # Restore data
            memory.conversation_history = data.get("conversation_history", [])
            memory.session_context = data.get("session_context", {})
            
            learning_state = data.get("learning_state", {})
            memory.current_topic = learning_state.get("current_topic")
            memory.learning_objectives = learning_state.get("objectives", [])
            memory.completed_objectives = learning_state.get("completed_objectives", [])
            
            agent_state = data.get("agent_state", {})
            memory.active_agents = agent_state.get("active_agents", [])
            memory.agent_handoffs = agent_state.get("handoffs", [])
            
            return memory
            
        except Exception as e:
            print(f"Error loading session memory: {e}")
            return None
    
    class Config:
        arbitrary_types_allowed = True
