"""
Practice domain agents for the GAAPF constellation system.

This module implements agents specialized in practical learning:
- CodeAssistantAgent: Provides code examples and implementation help
- PracticeFacilitatorAgent: Creates exercises and hands-on activities
- ProjectGuideAgent: Guides through real-world project development
- TroubleshooterAgent: Helps debug and solve technical problems
"""

from typing import Dict, Any, List, Optional
from datetime import datetime

from .base_agent import BaseAgent, AgentResponse, HandoffSuggestion, HandoffConfidence
from ..config.user_profiles import UserProfile
from ..config.framework_configs import SupportedFrameworks
from ..config.constellation_configs import AgentRole


class CodeAssistantAgent(BaseAgent):
    """
    Agent specialized in providing code examples and implementation assistance.
    
    Specializes in:
    - Code examples and snippets
    - Implementation guidance
    - Code explanation and walkthrough
    - Best practices demonstration
    """
    
    def __init__(self, llm_client: Any, **kwargs):
        system_prompt = """You are a code assistant specializing in Python AI frameworks.
Your role is to provide clear, working code examples and implementation guidance.

Key responsibilities:
- Provide working code examples
- Explain code line by line when needed
- Demonstrate best practices
- Show multiple implementation approaches
- Help with code debugging and optimization

Communication style:
- Always include working code examples
- Use clear comments in code
- Explain complex parts step by step
- Show both simple and advanced versions
- Encourage experimentation with code"""

        super().__init__(
            agent_role=AgentRole.CODE_ASSISTANT,
            llm_client=llm_client,
            system_prompt=system_prompt,
            handoff_keywords=[
                "practice", "exercise", "try", "hands-on", "project", "build",
                "error", "debug", "problem", "issue", "troubleshoot"
            ],
            **kwargs
        )
    
    async def generate_response(
        self,
        user_message: str,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
        session_context: Dict[str, Any],
    ) -> AgentResponse:
        """Generate code-focused response."""
        start_time = datetime.utcnow()
        
        system_prompt = self._build_system_prompt(user_profile, framework, module_id)
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_message}
        ]
        
        content = await self._call_llm(messages)
        
        self.add_to_history("user", user_message)
        self.add_to_history("assistant", content)
        
        handoff_suggestion = self.analyze_handoff_opportunity(user_message, session_context)
        processing_time = (datetime.utcnow() - start_time).total_seconds()
        
        return AgentResponse(
            agent_role=self.agent_role,
            content=content,
            response_type="code_assistance",
            handoff_suggestion=handoff_suggestion,
            learning_objectives_addressed=["practical_implementation", "coding_skills"],
            difficulty_level=self._assess_code_difficulty(user_profile),
            estimated_reading_time_minutes=len(content.split()) / 150,  # Slower for code
            includes_code_example=True,  # Code assistant always includes code
            includes_exercise="exercise" in content.lower(),
            processing_time_seconds=processing_time,
        )
    
    def analyze_handoff_opportunity(
        self,
        user_message: str,
        current_context: Dict[str, Any],
    ) -> Optional[HandoffSuggestion]:
        """Analyze handoff opportunities from code context."""
        message_lower = user_message.lower()
        
        # Check for practice requests
        if any(keyword in message_lower for keyword in ["practice", "exercise", "more examples"]):
            return HandoffSuggestion(
                target_agent=AgentRole.PRACTICE_FACILITATOR,
                confidence=HandoffConfidence.HIGH,
                reason="User wants structured practice after seeing code examples",
                context_data={"code_examples_provided": True}
            )
        
        # Check for project requests
        if any(keyword in message_lower for keyword in ["project", "build", "create app"]):
            return HandoffSuggestion(
                target_agent=AgentRole.PROJECT_GUIDE,
                confidence=HandoffConfidence.HIGH,
                reason="User wants to build a project using the code concepts",
                context_data={"implementation_knowledge": True}
            )
        
        # Check for debugging requests
        if any(keyword in message_lower for keyword in ["error", "debug", "problem", "not working"]):
            return HandoffSuggestion(
                target_agent=AgentRole.TROUBLESHOOTER,
                confidence=HandoffConfidence.CRITICAL,
                reason="User has encountered technical problems that need debugging",
                context_data={"code_context": user_message}
            )
        
        return None
    
    def _assess_code_difficulty(self, user_profile: UserProfile) -> str:
        """Assess appropriate code difficulty level."""
        if user_profile.python_skill_level.value in ["none", "beginner"]:
            return "beginner"
        elif user_profile.python_skill_level.value == "intermediate":
            return "intermediate"
        else:
            return "advanced"


class PracticeFacilitatorAgent(BaseAgent):
    """
    Agent specialized in creating exercises and hands-on learning activities.
    
    Specializes in:
    - Interactive exercises
    - Skill-building activities
    - Progressive challenges
    - Practice session guidance
    """
    
    def __init__(self, llm_client: Any, **kwargs):
        system_prompt = """You are a practice facilitator for Python AI frameworks.
Your role is to create engaging exercises and hands-on learning activities.

Key responsibilities:
- Design progressive exercises
- Create interactive challenges
- Provide step-by-step practice guidance
- Offer multiple difficulty levels
- Give constructive feedback on attempts

Communication style:
- Be encouraging and supportive
- Provide clear instructions
- Break exercises into manageable steps
- Offer hints when users are stuck
- Celebrate progress and achievements"""

        super().__init__(
            agent_role=AgentRole.PRACTICE_FACILITATOR,
            llm_client=llm_client,
            system_prompt=system_prompt,
            handoff_keywords=[
                "explain", "theory", "concept", "why", "how does",
                "project", "build", "create", "assessment", "test"
            ],
            **kwargs
        )
    
    async def generate_response(
        self,
        user_message: str,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
        session_context: Dict[str, Any],
    ) -> AgentResponse:
        """Generate practice-focused response."""
        start_time = datetime.utcnow()
        
        system_prompt = self._build_system_prompt(user_profile, framework, module_id)
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_message}
        ]
        
        content = await self._call_llm(messages)
        
        self.add_to_history("user", user_message)
        self.add_to_history("assistant", content)
        
        handoff_suggestion = self.analyze_handoff_opportunity(user_message, session_context)
        processing_time = (datetime.utcnow() - start_time).total_seconds()
        
        return AgentResponse(
            agent_role=self.agent_role,
            content=content,
            response_type="practice_activity",
            handoff_suggestion=handoff_suggestion,
            learning_objectives_addressed=["hands_on_practice", "skill_development"],
            difficulty_level=self._assess_practice_difficulty(user_profile),
            estimated_reading_time_minutes=len(content.split()) / 180,
            includes_code_example="```" in content,
            includes_exercise=True,  # Practice facilitator always includes exercises
            processing_time_seconds=processing_time,
        )
    
    def analyze_handoff_opportunity(
        self,
        user_message: str,
        current_context: Dict[str, Any],
    ) -> Optional[HandoffSuggestion]:
        """Analyze handoff opportunities from practice context."""
        message_lower = user_message.lower()
        
        # Check for conceptual questions
        if any(keyword in message_lower for keyword in ["why", "how does", "explain", "concept"]):
            return HandoffSuggestion(
                target_agent=AgentRole.INSTRUCTOR,
                confidence=HandoffConfidence.MEDIUM,
                reason="User needs conceptual explanation during practice",
                context_data={"practice_context": True}
            )
        
        # Check for project building interest
        if any(keyword in message_lower for keyword in ["project", "build something", "create app"]):
            return HandoffSuggestion(
                target_agent=AgentRole.PROJECT_GUIDE,
                confidence=HandoffConfidence.HIGH,
                reason="User ready to move from exercises to project building",
                context_data={"practice_completed": True}
            )
        
        return None
    
    def _assess_practice_difficulty(self, user_profile: UserProfile) -> str:
        """Assess appropriate practice difficulty level."""
        base_level = user_profile.python_skill_level.value
        
        # Adjust based on learning pace
        if user_profile.learning_pace.value == "intensive":
            if base_level == "beginner":
                return "intermediate"
            elif base_level == "intermediate":
                return "advanced"
        
        return base_level


class ProjectGuideAgent(BaseAgent):
    """
    Agent specialized in guiding real-world project development.
    
    Specializes in:
    - Project planning and architecture
    - Step-by-step project guidance
    - Real-world application development
    - Project best practices
    """
    
    def __init__(self, llm_client: Any, **kwargs):
        system_prompt = """You are a project guide for Python AI framework applications.
Your role is to guide users through building real-world projects and applications.

Key responsibilities:
- Help plan project architecture
- Provide step-by-step project guidance
- Suggest best practices for project structure
- Guide through implementation phases
- Help with project deployment considerations

Communication style:
- Be practical and project-focused
- Break projects into manageable phases
- Provide clear milestones and checkpoints
- Encourage iterative development
- Focus on real-world applicability"""

        super().__init__(
            agent_role=AgentRole.PROJECT_GUIDE,
            llm_client=llm_client,
            system_prompt=system_prompt,
            handoff_keywords=[
                "code", "implement", "debug", "error", "problem",
                "assessment", "evaluate", "progress"
            ],
            **kwargs
        )
    
    async def generate_response(
        self,
        user_message: str,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
        session_context: Dict[str, Any],
    ) -> AgentResponse:
        """Generate project guidance response."""
        start_time = datetime.utcnow()
        
        system_prompt = self._build_system_prompt(user_profile, framework, module_id)
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_message}
        ]
        
        content = await self._call_llm(messages)
        
        self.add_to_history("user", user_message)
        self.add_to_history("assistant", content)
        
        handoff_suggestion = self.analyze_handoff_opportunity(user_message, session_context)
        processing_time = (datetime.utcnow() - start_time).total_seconds()
        
        return AgentResponse(
            agent_role=self.agent_role,
            content=content,
            response_type="project_guidance",
            handoff_suggestion=handoff_suggestion,
            learning_objectives_addressed=["project_development", "real_world_application"],
            difficulty_level="intermediate",
            estimated_reading_time_minutes=len(content.split()) / 170,
            includes_code_example="```" in content,
            has_project=True,
            processing_time_seconds=processing_time,
        )
    
    def analyze_handoff_opportunity(
        self,
        user_message: str,
        current_context: Dict[str, Any],
    ) -> Optional[HandoffSuggestion]:
        """Analyze handoff opportunities from project context."""
        message_lower = user_message.lower()
        
        # Check for implementation help
        if any(keyword in message_lower for keyword in ["implement", "code", "how to write"]):
            return HandoffSuggestion(
                target_agent=AgentRole.CODE_ASSISTANT,
                confidence=HandoffConfidence.HIGH,
                reason="User needs specific implementation help for project",
                context_data={"project_context": True}
            )
        
        # Check for debugging needs
        if any(keyword in message_lower for keyword in ["error", "debug", "not working", "problem"]):
            return HandoffSuggestion(
                target_agent=AgentRole.TROUBLESHOOTER,
                confidence=HandoffConfidence.CRITICAL,
                reason="User encountered project-related technical issues",
                context_data={"project_debugging": True}
            )
        
        return None


class TroubleshooterAgent(BaseAgent):
    """
    Agent specialized in debugging and solving technical problems.
    
    Specializes in:
    - Error diagnosis and resolution
    - Debugging strategies
    - Common problem solutions
    - Performance optimization
    """
    
    def __init__(self, llm_client: Any, **kwargs):
        system_prompt = """You are a troubleshooter for Python AI framework issues.
Your role is to help diagnose and solve technical problems and errors.

Key responsibilities:
- Diagnose errors and technical issues
- Provide step-by-step debugging guidance
- Suggest solutions and workarounds
- Explain common pitfalls and how to avoid them
- Help optimize code performance

Communication style:
- Be systematic and methodical
- Ask clarifying questions about the problem
- Provide step-by-step debugging steps
- Explain the root cause of issues
- Offer preventive measures"""

        super().__init__(
            agent_role=AgentRole.TROUBLESHOOTER,
            llm_client=llm_client,
            system_prompt=system_prompt,
            handoff_keywords=[
                "explain", "why", "concept", "mentor", "guidance"
            ],
            **kwargs
        )
    
    async def generate_response(
        self,
        user_message: str,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
        session_context: Dict[str, Any],
    ) -> AgentResponse:
        """Generate troubleshooting response."""
        start_time = datetime.utcnow()
        
        system_prompt = self._build_system_prompt(user_profile, framework, module_id)
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_message}
        ]
        
        content = await self._call_llm(messages)
        
        self.add_to_history("user", user_message)
        self.add_to_history("assistant", content)
        
        handoff_suggestion = self.analyze_handoff_opportunity(user_message, session_context)
        processing_time = (datetime.utcnow() - start_time).total_seconds()
        
        return AgentResponse(
            agent_role=self.agent_role,
            content=content,
            response_type="troubleshooting",
            handoff_suggestion=handoff_suggestion,
            learning_objectives_addressed=["problem_solving", "debugging_skills"],
            difficulty_level="intermediate",
            estimated_reading_time_minutes=len(content.split()) / 160,
            includes_code_example="```" in content,
            processing_time_seconds=processing_time,
        )
    
    def analyze_handoff_opportunity(
        self,
        user_message: str,
        current_context: Dict[str, Any],
    ) -> Optional[HandoffSuggestion]:
        """Analyze handoff opportunities from troubleshooting context."""
        message_lower = user_message.lower()
        
        # Check for conceptual questions after debugging
        if any(keyword in message_lower for keyword in ["why", "explain", "concept", "understand"]):
            return HandoffSuggestion(
                target_agent=AgentRole.INSTRUCTOR,
                confidence=HandoffConfidence.MEDIUM,
                reason="User needs conceptual explanation after troubleshooting",
                context_data={"problem_solved": True}
            )
        
        return None
