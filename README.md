# GAAPF - Guidance AI Agent for Python Framework

🤖 **An Adaptive Multi-Agent Learning System for AI Framework Education**

GAAPF is a cutting-edge educational platform that uses the novel "Adaptive Learning Constellation" architecture to provide personalized, interactive learning experiences for Python AI frameworks. Built with LangChain, LangGraph, and advanced temporal optimization algorithms.

## 🌟 Key Features

### 🔗 Adaptive Learning Constellations
- **Dynamic Agent Networks**: Multi-agent systems that adapt in real-time based on user learning patterns
- **Specialized Agent Roles**: <PERSON><PERSON><PERSON><PERSON>, Code Assistant, Documentation Expert, Practice Facilitator, and more
- **Context-Aware Handoffs**: Intelligent agent coordination for seamless learning experiences

### 📊 Temporal Learning Optimization
- **Effectiveness Tracking**: Continuous monitoring of learning outcomes and engagement
- **Pattern Recognition**: AI-powered analysis of optimal learning configurations
- **Personalized Recommendations**: Constellation selection based on individual learning patterns

### 🎯 Comprehensive Framework Support
- **LangChain**: Complete learning path from basics to advanced agent systems
- **LangGraph**: Stateful multi-agent application development
- **Extensible Architecture**: Easy addition of new frameworks (CrewAI, AutoGen, LlamaIndex coming soon)

### 🚀 Modern Technology Stack
- **LangChain 0.3.x**: Latest LLM orchestration framework
- **LangGraph 0.4.x**: Advanced graph-based agent workflows
- **Streamlit**: Beautiful, interactive web interface
- **Pydantic 2.x**: Type-safe configuration and data validation
- **Modern Python**: Built for Python 3.10+

## 🏗️ System Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    User Interface Layer                     │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   CLI Interface │ │  Streamlit Web  │ │   FastAPI REST  │ │
│  │  (Real LLM)     │ │   (Demo Mode)   │ │      API        │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│              Intelligent Agent Management Layer            │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │            Adaptive Learning Constellation              │ │
│  │  ┌───────────┐ ┌───────────┐ ┌───────────┐ ┌───────────┐│ │
│  │  │Instructor │ │Code Assist│ │Doc Expert │ │Assessment ││ │
│  │  └───────────┘ └───────────┘ └───────────┘ └───────────┘│ │
│  │  ┌───────────┐ ┌───────────┐ ┌───────────┐ ┌───────────┐│ │
│  │  │ Mentor    │ │Practice   │ │Research   │ │Project    ││ │
│  │  │           │ │Facilitator│ │Assistant  │ │Guide      ││ │
│  │  └───────────┘ └───────────┘ └───────────┘ └───────────┘│ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│               Core Orchestration Layer                     │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │ Constellation   │ │ Temporal State  │ │ Adaptive Engine │ │
│  │ Manager         │ │ Manager         │ │                 │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │ Learning Hub    │ │ Analytics       │ │ Knowledge Graph │ │
│  │                 │ │ Engine          │ │                 │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    LLM Integration Layer                   │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │  Gemini 2.5     │ │   OpenAI GPT    │ │ Anthropic       │ │
│  │  Flash/Pro      │ │   3.5/4.0       │ │ Claude          │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                Configuration & Storage Layer               │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │ User Profiles   │ │Framework Configs│ │ Memory Storage  │ │
│  │ (JSON Files)    │ │ (JSON/Python)   │ │ (Chroma DB)     │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │ Learning        │ │ Constellation   │ │ Temporal        │ │
│  │ Sessions        │ │ Memory          │ │ Patterns        │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🤖 Agent Architecture - Adaptive Learning Constellation

```
┌─────────────────────────────────────────────────────────────┐
│                    Agent Constellation Architecture         │
└─────────────────────────────────────────────────────────────┘

                          ┌─────────────────────┐
                          │  ConstellationManager │
                          │  (Agent Orchestrator) │
                          └──────────┬──────────┘
                                     │
                     ┌───────────────┼───────────────┐
                     │               │               │
          ┌──────────▼─────────┐ ┌──▼──┐ ┌─────────▼─────────┐
          │  Knowledge Agents  │ │Base │ │   Practice Agents │
          └────────────────────┘ │Agent│ └───────────────────┘
          │ 📚 Instructor       │ │     │ │ ⚡ Code Assistant  │
          │ 📖 Doc Expert       │ │Core │ │ 🛠️ Practice Facilitator│
          │ 🔬 Research Assistant│ │     │ │ 🏗️ Project Guide   │
          │ 🧠 Knowledge Synth. │ │     │ │ 🔧 Troubleshooter  │
          └────────────────────┘ └──┬──┘ └───────────────────┘
                     │               │               │
          ┌──────────▼─────────┐    │    ┌─────────▼─────────┐
          │  Support Agents    │    │    │ Assessment Agents │
          └────────────────────┘    │    └───────────────────┘
          │ 🎯 Mentor          │    │    │ 📊 Assessment     │
          │ 💪 Motivational    │    │    │ 📈 Progress Tracker│
          │    Coach           │    │    └───────────────────┘
          └────────────────────┘    │
                                    │
                ┌───────────────────▼───────────────────┐
                │         Agent Communication           │
                │  ┌─────────────────────────────────┐  │
                │  │    Intelligent Handoff Logic   │  │
                │  │  • Content Analysis            │  │
                │  │  • Context Evaluation          │  │
                │  │  • Next Agent Suggestion       │  │
                │  │  • Confidence Scoring          │  │
                │  └─────────────────────────────────┘  │
                └───────────────────────────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Python 3.10 or higher
- At least one LLM API key:
  - **Google Gemini** (Recommended - free tier available)
  - **OpenAI GPT** (Pay per use)
  - **Anthropic Claude** (Pay per use)

### Installation

1. **Clone the repository**
```bash
git clone https://github.com/your-username/gaapf-guidance-ai-agent.git
cd gaapf-guidance-ai-agent
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Configure environment**
```bash
cp env.example .env
# Edit .env with your API keys
```

4. **Run the CLI (Recommended)**
```bash
python run_cli.py
```

**Alternative interfaces:**

5a. **Launch the Streamlit interface** (Demo mode - mock responses)
```bash
streamlit run src/pyframeworks_assistant/interfaces/web/streamlit_app.py
```

5b. **Run API server**
```bash
python -m src.pyframeworks_assistant.interfaces.api.main
```

### 🎯 CLI Interface (Real LLM Integration)

The CLI provides the full experience with **actual AI responses**:

- ✅ **Real LLM API calls** to Google Gemini, OpenAI GPT, or Anthropic Claude
- ✅ **Intelligent agent selection** based on your questions
- ✅ **Personalized learning paths** adapted to your skill level
- ✅ **Natural conversation** with specialized AI agents
- ✅ **Progress tracking** and temporal optimization

**Quick CLI Demo:**
```bash
# Start the CLI
python run_cli.py

# Follow the interactive setup:
# 1. Profile creation (experience, skills, goals)
# 2. Framework selection (LangChain, LangGraph, etc.)
# 3. Start learning with real AI assistance!

# Example conversation:
You: What is LangChain and how do I get started?
🤖 Instructor: LangChain is a powerful framework for building applications with Large Language Models...

You: Show me a simple code example
🤖 Code Assistant: Here's a basic LangChain example to get you started:
```python
from langchain.llms import OpenAI
...
```
```

See [CLI_GUIDE.md](CLI_GUIDE.md) for complete CLI documentation.

### Configuration

Create a `.env` file with your API keys:

```env
# LLM API Keys
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# LangSmith Configuration (Optional)
LANGCHAIN_TRACING_V2=true
LANGCHAIN_API_KEY=your_langsmith_api_key_here
LANGCHAIN_PROJECT=gaapf-guidance-ai-agent

# Database Configuration
REDIS_URL=redis://localhost:6379/0
POSTGRES_URL=postgresql://user:password@localhost:5432/pyframeworks_db
```

## 🎯 Usage Examples

### Creating a User Profile

```python
from pyframeworks_assistant.config.user_profiles import (
    UserProfile, SkillLevel, LearningPace, LearningStyle
)

profile = UserProfile(
    user_id="user_123",
    programming_experience_years=3,
    python_skill_level=SkillLevel.INTERMEDIATE,
    learning_pace=LearningPace.MODERATE,
    preferred_learning_style=LearningStyle.HANDS_ON,
    learning_goals=["Learn LangChain", "Build RAG applications"]
)
```

### Using the Constellation System

```python
import asyncio
from pyframeworks_assistant.core.constellation import ConstellationManager
from pyframeworks_assistant.config.framework_configs import SupportedFrameworks

async def learning_session():
    manager = ConstellationManager()
    
    # Create an adaptive constellation
    constellation = await manager.create_constellation(
        constellation_type=ConstellationType.HANDS_ON_FOCUSED,
        user_profile=profile,
        framework=SupportedFrameworks.LANGCHAIN,
        module_id="lc_basics",
        session_id="session_123"
    )
    
    # Run learning session
    result = await manager.run_session(
        session_id="session_123",
        user_message="I want to learn about LangChain chains",
        user_profile=profile,
        framework=SupportedFrameworks.LANGCHAIN,
        module_id="lc_basics"
    )
    
    return result

# Run the session
result = asyncio.run(learning_session())
```

### Temporal Optimization

```python
from pyframeworks_assistant.core.temporal_state import TemporalStateManager

temporal_manager = TemporalStateManager()

# Get optimal constellation for user
optimal_constellation, confidence = await temporal_manager.optimize_constellation_selection(
    user_profile=profile,
    framework=SupportedFrameworks.LANGCHAIN,
    module_id="lc_basics",
    session_context={}
)

print(f"Recommended: {optimal_constellation.value} (confidence: {confidence:.2f})")
```

## 📚 Framework Support

### Currently Supported

| Framework | Version | Status | Learning Modules |
|-----------|---------|--------|------------------|
| LangChain | 0.3.25+ | ✅ Full Support | 3 modules (12 hours) |
| LangGraph | 0.4.7+ | ✅ Full Support | 1 module (15 hours) |

### Coming Soon

| Framework | Target Version | Status | ETA |
|-----------|---------------|--------|-----|
| CrewAI | Latest | 🔄 In Development | Q2 2025 |
| AutoGen | Latest | 📋 Planned | Q2 2025 |
| LlamaIndex | Latest | 📋 Planned | Q3 2025 |

## 🔄 Simple Workflow Overview

```
┌─────────────────────────────────────────────────────────────────┐
│                     GAAPF Learning Workflow                    │
└─────────────────────────────────────────────────────────────────┘

1. USER ONBOARDING
   👤 User starts → Profile Creation → Framework Selection

2. CONSTELLATION FORMATION
   🔍 Analyze Context → 🎯 Select Optimal Agents → ⭐ Form Constellation

3. LEARNING SESSION
   💬 User Question → 🤖 Agent Processing → 🔄 Intelligent Handoffs
   
4. ADAPTIVE OPTIMIZATION  
   📊 Track Effectiveness → 🧠 Learn Patterns → 🎯 Optimize Future Sessions

5. PROGRESS SYNTHESIS
   📈 Update Progress → 💾 Save Session → 🚀 Plan Next Learning

┌─────────────────────────────────────────────────────────────────┐
│                    Detailed User Journey                       │
└─────────────────────────────────────────────────────────────────┘

START → User launches CLI
   ↓
PROFILE → Create/Load user profile
   ↓
FRAMEWORK → Choose: LangChain, LangGraph, CrewAI, etc.
   ↓
CONSTELLATION → System forms optimal agent team
   ↓
LEARNING LOOP:
   ├─ User asks question
   ├─ Primary agent responds
   ├─ Handoff to specialist if needed
   ├─ Practice exercises generated
   ├─ Progress tracked
   └─ Loop continues...
   ↓
ADAPTATION → System learns user patterns
   ↓
SYNTHESIS → Session summary & next steps
   ↓
END → Save progress & exit gracefully
```

## 🏛️ Constellation Types

GAAPF offers 8 specialized constellation types:

1. **Knowledge Intensive** 📚
   - Focus: Theoretical understanding
   - Agents: Instructor, Documentation Expert, Knowledge Synthesizer
   - Best for: Conceptual learning, theoretical foundations

2. **Hands-On Focused** ⚡
   - Focus: Practical implementation
   - Agents: Code Assistant, Practice Facilitator, Project Guide
   - Best for: Learning by doing, practical skills

3. **Theory-Practice Balanced** ⚖️
   - Focus: Balanced approach
   - Agents: Instructor, Code Assistant, Mentor
   - Best for: Comprehensive understanding

4. **Research Intensive** 🔬
   - Focus: Deep exploration
   - Agents: Research Assistant, Documentation Expert, Knowledge Synthesizer
   - Best for: Advanced topics, cutting-edge features

5. **Quick Learning** 🚀
   - Focus: Rapid skill acquisition
   - Agents: Instructor, Code Assistant, Progress Tracker
   - Best for: Time-constrained learning

6. **Deep Exploration** 🌊
   - Focus: Thorough understanding
   - Agents: Research Assistant, Mentor, Knowledge Synthesizer
   - Best for: Mastery-oriented learning

7. **Project Oriented** 🏗️
   - Focus: Real-world application
   - Agents: Project Guide, Code Assistant, Troubleshooter
   - Best for: Building actual projects

8. **Assessment Focused** 📊
   - Focus: Progress evaluation
   - Agents: Assessment Agent, Progress Tracker, Motivational Coach
   - Best for: Certification preparation

## 🧪 Testing

Run the comprehensive test suite:

```bash
# Basic functionality test
python test_basic.py

# Unit tests (when available)
pytest tests/unit/

# Integration tests (when available)
pytest tests/integration/
```

## 🛠️ Development

### Project Structure

```
gaapf-guidance-ai-agent/
├── src/
│   └── pyframeworks_assistant/
│       ├── config/           # Configuration management
│       ├── core/             # Core constellation system
│       ├── agents/           # Individual agent implementations
│       ├── memory/           # Memory and persistence
│       ├── tools/            # Utility tools and integrations
│       └── interfaces/       # User interfaces
├── tests/                    # Test suite
├── docs/                     # Documentation
├── requirements.txt          # Dependencies
└── pyproject.toml           # Project configuration
```

### Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Setup

```bash
# Install development dependencies
pip install -r requirements.txt

# Run tests
python test_basic.py

# Run linting
ruff check src/
black src/

# Type checking
mypy src/
```

## 📊 Performance & Metrics

### Learning Effectiveness Metrics

- **Comprehension Score**: Understanding level measurement
- **Engagement Score**: User interaction and interest tracking
- **Completion Rate**: Task and module completion tracking
- **Time Efficiency**: Learning speed optimization
- **Retention Estimate**: Knowledge retention prediction

### System Performance

- **Constellation Adaptation**: < 2 seconds response time
- **Pattern Recognition**: Real-time learning optimization
- **Memory Management**: Efficient session state handling
- **Scalability**: Supports concurrent multi-user sessions

## 🔧 Configuration Options

### User Profile Customization

```python
# Skill levels: none, beginner, intermediate, advanced, expert
# Learning paces: slow, moderate, fast, intensive
# Learning styles: visual, hands_on, theoretical, mixed
# Difficulty progression: gradual, moderate, aggressive
# Feedback styles: encouraging, direct, detailed, minimal
```

### System Configuration

```python
# Constellation settings
max_concurrent_agents = 16
constellation_timeout = 300  # seconds
role_morphing_enabled = True

# Temporal optimization
effectiveness_tracking_enabled = True
pattern_analysis_enabled = True
optimization_auto_apply = False

# Memory management
max_memory_sessions = 1000
memory_cleanup_interval = 3600  # seconds
```

## 🤝 Community & Support

- **Documentation**: [Coming Soon]
- **Issues**: [GitHub Issues](https://github.com/your-username/gaapf-guidance-ai-agent/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/gaapf-guidance-ai-agent/discussions)
- **Discord**: [Coming Soon]

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **LangChain Team** for the incredible LLM orchestration framework
- **LangGraph Team** for advanced graph-based agent capabilities  
- **Streamlit Team** for the beautiful web interface framework
- **The AI Community** for continuous inspiration and collaboration

## 🔮 Roadmap

### Phase 1: Core System (Current)
- ✅ Adaptive learning constellations
- ✅ LangChain & LangGraph support
- ✅ Temporal optimization
- ✅ Streamlit interface

### Phase 2: Enhanced Features (Q2 2025)
- 🔄 Additional framework support (CrewAI, AutoGen)
- 🔄 Advanced analytics dashboard
- 🔄 Multi-language support
- 🔄 API-first architecture

### Phase 3: Enterprise Features (Q3 2025)
- 📋 Team collaboration features
- 📋 Advanced assessment tools
- 📋 Integration with learning management systems
- 📋 Custom constellation creation

### Phase 4: Advanced AI (Q4 2025)
- 📋 Autonomous curriculum generation
- 📋 Cross-framework learning paths
- 📋 AI-powered content creation
- 📋 Predictive learning analytics

---

<div align="center">

**Built with ❤️ for the AI learning community**

[⭐ Star us on GitHub](https://github.com/your-username/gaapf-guidance-ai-agent) | [🐛 Report Issues](https://github.com/your-username/gaapf-guidance-ai-agent/issues) | [💡 Request Features](https://github.com/your-username/gaapf-guidance-ai-agent/discussions)

</div> 