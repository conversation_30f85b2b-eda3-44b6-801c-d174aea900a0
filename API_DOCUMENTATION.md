# GAAPF API Documentation

Complete REST API documentation for integrating GAAPF functionality into your applications.

## 🌐 Base URL

```
http://localhost:8000
```

## 🔧 Running the API

### Development Server

```bash
# Install dependencies
pip install -r requirements.txt

# Run the API server
python -m uvicorn src.pyframeworks_assistant.interfaces.api.main:app --reload --host 0.0.0.0 --port 8000
```

### Production Deployment

```bash
# Using uvicorn directly
uvicorn src.pyframeworks_assistant.interfaces.api.main:app --host 0.0.0.0 --port 8000 --workers 4

# Using gunicorn
gunicorn src.pyframeworks_assistant.interfaces.api.main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

## 📚 API Endpoints

### System Information

#### Get System Info
```http
GET /api/v1/system/info
```

**Response:**
```json
{
  "name": "GAAPF - Guidance AI Agent for Python Framework",
  "version": "1.0.0",
  "description": "An Adaptive Multi-Agent Learning System for AI Framework Education",
  "supported_frameworks": ["langchain", "langgraph"],
  "constellation_types": ["knowledge_intensive", "hands_on_focused", ...],
  "features": [
    "Adaptive Learning Constellations",
    "Temporal Optimization",
    "Multi-Agent Intelligence",
    "Personalized Learning Paths"
  ]
}
```

#### Get Available Frameworks
```http
GET /api/v1/frameworks
```

**Response:**
```json
{
  "frameworks": [
    {
      "id": "langchain",
      "name": "LangChain",
      "description": "A framework for developing applications powered by language models",
      "version": "0.3.25+",
      "modules": 3,
      "estimated_hours": 18.0,
      "supported": true
    }
  ]
}
```

#### Get Available Constellations
```http
GET /api/v1/constellations
```

**Response:**
```json
{
  "constellations": [
    {
      "id": "hands_on_focused",
      "name": "Hands-On Focused",
      "description": "Emphasis on practical implementation and learning by doing",
      "focus": "Practical skills and implementation",
      "primary_agents": ["code_assistant", "practice_facilitator", "project_guide"],
      "support_agents": ["troubleshooter"],
      "max_agents": 4
    }
  ]
}
```

### User Management

#### Create User Profile
```http
POST /api/v1/users
```

**Request Body:**
```json
{
  "user_id": "john_doe",
  "name": "John Doe",
  "programming_experience_years": 3,
  "python_skill_level": "intermediate",
  "learning_pace": "moderate",
  "preferred_learning_style": "hands_on",
  "learning_goals": ["Learn LangChain", "Build chatbots"]
}
```

**Response:**
```json
{
  "message": "User profile created successfully",
  "user_id": "john_doe",
  "profile": {
    "name": "John Doe",
    "experience_level": "Intermediate",
    "python_skill": "intermediate",
    "learning_style": "hands_on",
    "goals": ["Learn LangChain", "Build chatbots"]
  }
}
```

#### Get User Profile
```http
GET /api/v1/users/{user_id}
```

**Response:**
```json
{
  "user_id": "john_doe",
  "message": "User profile retrieval not implemented in demo",
  "note": "In the full system, this would return complete user profile data"
}
```

### Learning Sessions

#### Start Learning Session
```http
POST /api/v1/sessions/start
```

**Request Body:**
```json
{
  "user_id": "john_doe",
  "framework": "langchain",
  "module_id": "lc_basics",
  "constellation_type": "hands_on_focused"
}
```

**Response:**
```json
{
  "message": "Learning session started successfully",
  "session_info": {
    "session_id": "john_doe_20241201_143022",
    "constellation_type": "hands_on_focused",
    "primary_agent": "code_assistant",
    "module_info": {
      "title": "LangChain Fundamentals",
      "description": "Learn the core concepts and basic usage of LangChain"
    },
    "learning_path_progress": {
      "current_module": "lc_basics",
      "completion_percentage": 0.0,
      "modules_completed": 0,
      "total_modules": 3
    },
    "welcome_message": "Welcome to your LangChain learning session! 🚀..."
  }
}
```

#### Process Learning Interaction
```http
POST /api/v1/sessions/interact
```

**Request Body:**
```json
{
  "session_id": "john_doe_20241201_143022",
  "user_message": "What is LangChain?",
  "user_id": "john_doe"
}
```

**Response:**
```json
{
  "message": "Interaction processed successfully",
  "result": {
    "response": {
      "agent_role": "instructor",
      "content": "LangChain is a powerful framework for building applications with Large Language Models...",
      "response_type": "instructional",
      "handoff_suggestion": null,
      "learning_objectives_addressed": ["conceptual_understanding"],
      "difficulty_level": "intermediate",
      "estimated_reading_time_minutes": 2.5,
      "includes_code_example": false,
      "includes_exercise": false,
      "processing_time_seconds": 1.2
    },
    "session_info": {
      "interactions": 1,
      "duration_minutes": 2.3,
      "current_agent": "instructor"
    },
    "engagement_metrics": {
      "interaction_rate": 0.43,
      "session_momentum": "building",
      "content_complexity": "intermediate",
      "engagement_level": "building"
    },
    "learning_suggestions": [
      "Ask for a practical example to see the concept in action",
      "Request a step-by-step explanation if something is unclear"
    ]
  }
}
```

#### End Learning Session
```http
POST /api/v1/sessions/{session_id}/end
```

**Request Body (Optional):**
```json
{
  "session_id": "john_doe_20241201_143022",
  "satisfaction": 0.8,
  "comprehension": 0.7
}
```

**Response:**
```json
{
  "message": "Learning session ended successfully",
  "summary": {
    "session_summary": {
      "duration_minutes": 45.2,
      "interactions": 23,
      "effectiveness_score": 0.853,
      "constellation_used": "hands_on_focused",
      "handoffs": 4
    },
    "learning_progress": {
      "module_completed": "lc_basics",
      "total_completion": 33.3,
      "next_module": "lc_intermediate",
      "hours_invested": 12.5
    },
    "recommendations": [
      "Excellent session! Consider tackling more advanced topics.",
      "You're ready to move to the next module."
    ]
  }
}
```

#### Get Session Status
```http
GET /api/v1/sessions/{session_id}/status
```

**Response:**
```json
{
  "session_id": "john_doe_20241201_143022",
  "status": {
    "constellation_id": "john_doe_20241201_143022_hands_on_focused",
    "constellation_type": "hands_on_focused",
    "status": "active",
    "primary_agent": "code_assistant",
    "active_agents": ["code_assistant", "practice_facilitator", "troubleshooter"],
    "total_interactions": 23,
    "handoff_count": 4,
    "created_at": "2024-12-01T14:30:22",
    "last_activity_at": "2024-12-01T15:15:30"
  }
}
```

### Analytics

#### Get User Analytics
```http
GET /api/v1/analytics/user/{user_id}?days=30
```

**Response:**
```json
{
  "user_id": "john_doe",
  "time_period_days": 30,
  "analytics": {
    "overview": {
      "total_sessions": 15,
      "total_learning_hours": 24.5,
      "average_session_length": 98.0,
      "learning_streak": 7,
      "consistency_score": 0.85
    },
    "preferences": {
      "preferred_constellation": "hands_on_focused",
      "optimal_session_length": 45.0,
      "constellation_scores": {
        "hands_on_focused": 0.87,
        "theory_practice_balanced": 0.78
      }
    },
    "progress": {
      "frameworks": {
        "langchain": {
          "mastery_level": "intermediate",
          "completion_percentage": 66.7,
          "time_invested": 18.5,
          "effectiveness": 0.82
        }
      },
      "total_modules_completed": 4,
      "current_goals": ["Learn LangChain", "Build chatbots"]
    }
  }
}
```

#### Get System Analytics
```http
GET /api/v1/analytics/system?days=7
```

**Response:**
```json
{
  "message": "System analytics not implemented in demo",
  "note": "In the full system, this would return comprehensive system metrics",
  "time_period_days": 7
}
```

### Demo Endpoints

#### Demo Chat Response
```http
GET /api/v1/demo/chat?message=What is LangChain?
```

**Response:**
```json
{
  "message": "Demo response generated",
  "user_message": "What is LangChain?",
  "response": {
    "agent": "instructor",
    "content": "LangChain is a framework for developing applications powered by language models...",
    "type": "explanation",
    "includes_code": false
  },
  "timestamp": "2024-12-01T15:30:22"
}
```

## 🔒 Authentication

Currently, the API does not implement authentication. In a production environment, you would add:

- **API Key authentication** for service-to-service calls
- **JWT tokens** for user sessions
- **Rate limiting** to prevent abuse
- **CORS configuration** for web applications

## 📊 Response Codes

| Code | Description |
|------|-------------|
| 200 | Success |
| 201 | Created |
| 400 | Bad Request |
| 404 | Not Found |
| 422 | Validation Error |
| 500 | Internal Server Error |

## 🔧 Error Handling

All errors return a consistent format:

```json
{
  "error": "Error type",
  "detail": "Detailed error message"
}
```

## 📝 Usage Examples

### Python Client Example

```python
import requests
import json

# Base URL
BASE_URL = "http://localhost:8000"

# Create user profile
profile_data = {
    "user_id": "test_user",
    "name": "Test User",
    "python_skill_level": "intermediate",
    "learning_goals": ["Learn LangChain"]
}

response = requests.post(f"{BASE_URL}/api/v1/users", json=profile_data)
print(response.json())

# Start learning session
session_data = {
    "user_id": "test_user",
    "framework": "langchain"
}

response = requests.post(f"{BASE_URL}/api/v1/sessions/start", json=session_data)
session_info = response.json()
session_id = session_info["session_info"]["session_id"]

# Send learning interaction
interaction_data = {
    "session_id": session_id,
    "user_message": "What is LangChain?",
    "user_id": "test_user"
}

response = requests.post(f"{BASE_URL}/api/v1/sessions/interact", json=interaction_data)
print(response.json())
```

### JavaScript Client Example

```javascript
const BASE_URL = 'http://localhost:8000';

// Create user profile
const profileData = {
  user_id: 'test_user',
  name: 'Test User',
  python_skill_level: 'intermediate',
  learning_goals: ['Learn LangChain']
};

fetch(`${BASE_URL}/api/v1/users`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(profileData)
})
.then(response => response.json())
.then(data => console.log(data));

// Start learning session
const sessionData = {
  user_id: 'test_user',
  framework: 'langchain'
};

fetch(`${BASE_URL}/api/v1/sessions/start`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(sessionData)
})
.then(response => response.json())
.then(data => {
  const sessionId = data.session_info.session_id;
  // Use session ID for interactions
});
```

## 🚀 Interactive API Documentation

When the API is running, visit:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

These provide interactive documentation where you can test endpoints directly.

---

**Build amazing learning applications with GAAPF API! 🚀**
