"""
Agent implementations for the GAAPF constellation system.

This module contains all agent implementations organized by domain:
- Knowledge agents: Inst<PERSON>ctor, Documentation Expert, Research Assistant, Knowledge Synthesizer
- Practice agents: Code Assistant, Practice Facilitator, Project Guide, Troubleshooter
- Support agents: Mentor, Motivational Coach
- Assessment agents: Assessment Agent, Progress Tracker
"""

from .base_agent import BaseAgent, AgentResponse, HandoffSuggestion
from .knowledge_agents import (
    InstructorAgent,
    DocumentationExpertAgent,
    ResearchAssistantAgent,
    KnowledgeSynthesizerAgent,
)
from .practice_agents import (
    CodeAssistantAgent,
    PracticeFacilitatorAgent,
    ProjectGuideAgent,
    TroubleshooterAgent,
)
from .support_agents import (
    MentorAgent,
    MotivationalCoachAgent,
)
from .assessment_agents import (
    AssessmentAgent,
    ProgressTrackerAgent,
)

__all__ = [
    "BaseAgent",
    "AgentResponse",
    "HandoffSuggestion",
    "InstructorAgent",
    "DocumentationExpertAgent", 
    "ResearchAssistantAgent",
    "KnowledgeSynthesizerAgent",
    "CodeAssistantAgent",
    "PracticeFacilitatorAgent",
    "ProjectGuideAgent",
    "TroubleshooterAgent",
    "MentorAgent",
    "MotivationalCoachAgent",
    "AssessmentAgent",
    "ProgressTrackerAgent",
]
