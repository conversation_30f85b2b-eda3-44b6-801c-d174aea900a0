[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "gaapf-guidance-ai-agent"
version = "1.0.0"
description = "Guidance AI Agent for Python Framework - An Adaptive Multi-Agent Learning System"
readme = "README.md"
requires-python = ">=3.10"
license = {text = "MIT"}
authors = [
    {name = "GAAPF Team", email = "<EMAIL>"},
]
keywords = ["ai", "education", "langchain", "langgraph", "multi-agent", "learning"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Education",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Education",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
]

dependencies = [
    "langchain>=0.3.25",
    "langchain-community>=0.3.25",
    "langchain-core>=0.3.25",
    "langchain-openai>=0.2.0",
    "langchain-anthropic>=0.2.0",
    "langchain-google-genai>=2.0.0",
    "langgraph>=0.4.7",
    "langsmith>=0.1.0",
    "streamlit>=1.40.0",
    "fastapi>=0.115.0",
    "uvicorn>=0.32.0",
    "pydantic>=2.10.0",
    "pydantic-settings>=2.6.0",
    "chromadb>=0.5.0",
    "numpy>=1.24.0",
    "aiofiles>=24.0.0",
    "python-dotenv>=1.0.0",
    "rich>=13.0.0",
    "typer>=0.15.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=8.0.0",
    "pytest-asyncio>=0.24.0",
    "black>=24.0.0",
    "ruff>=0.8.0",
    "mypy>=1.13.0",
]
redis = ["redis>=5.0.0"]
postgres = ["psycopg2-binary>=2.9.0", "sqlalchemy>=2.0.0"]

[project.urls]
Homepage = "https://github.com/your-username/gaapf-guidance-ai-agent"
Documentation = "https://github.com/your-username/gaapf-guidance-ai-agent/docs"
Repository = "https://github.com/your-username/gaapf-guidance-ai-agent"
Issues = "https://github.com/your-username/gaapf-guidance-ai-agent/issues"

[project.scripts]
gaapf = "pyframeworks_assistant.interfaces.cli.cli_app:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.ruff]
target-version = "py310"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.mypy]
python_version = "3.10"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_return_any = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
asyncio_mode = "auto"
