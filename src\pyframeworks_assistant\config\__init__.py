"""
Configuration management for GAAPF system.

This module contains all configuration-related classes and utilities
for user profiles, framework configurations, and constellation settings.
"""

from .user_profiles import (
    UserProfile,
    SkillLevel,
    LearningPace,
    LearningStyle,
    DifficultyProgression,
    FeedbackStyle,
)
from .framework_configs import SupportedFrameworks, FrameworkConfig
from .constellation_configs import ConstellationType, ConstellationConfig

__all__ = [
    "UserProfile",
    "SkillLevel",
    "LearningPace", 
    "LearningStyle",
    "DifficultyProgression",
    "FeedbackStyle",
    "SupportedFrameworks",
    "FrameworkConfig",
    "ConstellationType",
    "ConstellationConfig",
]
