"""
Base agent implementation for the GAAPF constellation system.

This module provides the foundational BaseAgent class that all specialized
agents inherit from, along with common response and handoff structures.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum

from ..config.user_profiles import UserProfile
from ..config.framework_configs import SupportedFrameworks
from ..config.constellation_configs import AgentRole


class HandoffConfidence(str, Enum):
    """Confidence levels for agent handoffs."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class HandoffSuggestion(BaseModel):
    """Suggestion for handing off to another agent."""
    
    target_agent: AgentRole = Field(..., description="Suggested target agent")
    confidence: HandoffConfidence = Field(..., description="Confidence in handoff suggestion")
    reason: str = Field(..., description="Reason for handoff suggestion")
    context_data: Dict[str, Any] = Field(default_factory=dict, description="Context to pass to next agent")
    
    class Config:
        use_enum_values = True


class AgentResponse(BaseModel):
    """Response from an agent including content and metadata."""
    
    agent_role: AgentRole = Field(..., description="Role of responding agent")
    content: str = Field(..., description="Response content")
    response_type: str = Field("text", description="Type of response")
    
    # Handoff information
    handoff_suggestion: Optional[HandoffSuggestion] = Field(None, description="Handoff suggestion")
    
    # Learning metadata
    learning_objectives_addressed: List[str] = Field(default_factory=list)
    difficulty_level: str = Field("intermediate", description="Content difficulty level")
    estimated_reading_time_minutes: float = Field(2.0, description="Estimated reading time")
    
    # Engagement data
    includes_code_example: bool = Field(False, description="Whether response includes code")
    includes_exercise: bool = Field(False, description="Whether response includes exercise")
    includes_links: bool = Field(False, description="Whether response includes external links")
    
    # Metadata
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    processing_time_seconds: float = Field(0.0, description="Time taken to generate response")
    
    class Config:
        use_enum_values = True


class BaseAgent(ABC):
    """
    Base class for all GAAPF agents.
    
    Provides common functionality for agent coordination, handoff logic,
    and response generation while allowing specialized implementations.
    """
    
    def __init__(
        self,
        agent_role: AgentRole,
        llm_client: Any,
        system_prompt: str = "",
        **kwargs
    ):
        """Initialize base agent."""
        self.agent_role = agent_role
        self.llm_client = llm_client
        self.system_prompt = system_prompt
        self.conversation_history: List[Dict[str, Any]] = []
        self.context_data: Dict[str, Any] = {}
        
        # Agent-specific configuration
        self.max_response_length = kwargs.get("max_response_length", 2000)
        self.handoff_keywords = kwargs.get("handoff_keywords", [])
        self.specialization_areas = kwargs.get("specialization_areas", [])
    
    @abstractmethod
    async def generate_response(
        self,
        user_message: str,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
        session_context: Dict[str, Any],
    ) -> AgentResponse:
        """Generate a response to user input."""
        pass
    
    @abstractmethod
    def analyze_handoff_opportunity(
        self,
        user_message: str,
        current_context: Dict[str, Any],
    ) -> Optional[HandoffSuggestion]:
        """Analyze if a handoff to another agent would be beneficial."""
        pass
    
    def update_context(self, key: str, value: Any) -> None:
        """Update agent context data."""
        self.context_data[key] = value
    
    def get_context(self, key: str, default: Any = None) -> Any:
        """Get value from agent context."""
        return self.context_data.get(key, default)
    
    def add_to_history(self, role: str, content: str) -> None:
        """Add message to conversation history."""
        self.conversation_history.append({
            "role": role,
            "content": content,
            "timestamp": datetime.utcnow().isoformat(),
        })
    
    def get_recent_history(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get recent conversation history."""
        return self.conversation_history[-limit:] if self.conversation_history else []
    
    def clear_history(self) -> None:
        """Clear conversation history."""
        self.conversation_history.clear()
    
    def _detect_handoff_keywords(self, text: str) -> List[str]:
        """Detect handoff trigger keywords in text."""
        text_lower = text.lower()
        detected = []
        
        for keyword in self.handoff_keywords:
            if keyword.lower() in text_lower:
                detected.append(keyword)
        
        return detected
    
    def _calculate_handoff_confidence(
        self,
        keyword_matches: List[str],
        context_relevance: float,
        agent_capability_match: float,
    ) -> HandoffConfidence:
        """Calculate confidence level for handoff suggestion."""
        # Simple scoring algorithm
        score = 0.0
        
        # Keyword matching (0-40 points)
        score += min(len(keyword_matches) * 10, 40)
        
        # Context relevance (0-30 points)
        score += context_relevance * 30
        
        # Agent capability match (0-30 points)
        score += agent_capability_match * 30
        
        if score >= 70:
            return HandoffConfidence.CRITICAL
        elif score >= 50:
            return HandoffConfidence.HIGH
        elif score >= 30:
            return HandoffConfidence.MEDIUM
        else:
            return HandoffConfidence.LOW
    
    def _build_system_prompt(
        self,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
    ) -> str:
        """Build system prompt with context."""
        base_prompt = self.system_prompt
        
        # Add user context
        context_additions = f"""

User Context:
- Skill Level: {user_profile.python_skill_level}
- Learning Style: {user_profile.preferred_learning_style}
- Learning Pace: {user_profile.learning_pace}
- Experience: {user_profile.programming_experience_years} years
- Framework: {framework}
- Current Module: {module_id}

Adapt your responses to match the user's skill level and learning preferences.
"""
        
        return base_prompt + context_additions
    
    async def _call_llm(
        self,
        messages: List[Dict[str, str]],
        max_tokens: Optional[int] = None,
    ) -> str:
        """Call the LLM with error handling."""
        try:
            # This is a placeholder - actual implementation would depend on the LLM client
            # For now, return a mock response
            return f"[{self.agent_role.value}] Mock response to: {messages[-1].get('content', '')[:100]}..."
        except Exception as e:
            return f"I apologize, but I encountered an error while processing your request: {str(e)}"
    
    def get_agent_description(self) -> str:
        """Get a description of this agent's capabilities."""
        descriptions = {
            AgentRole.INSTRUCTOR: "I provide clear explanations and structured learning guidance.",
            AgentRole.CODE_ASSISTANT: "I help with code examples, debugging, and implementation.",
            AgentRole.DOCUMENTATION_EXPERT: "I provide detailed documentation and reference information.",
            AgentRole.RESEARCH_ASSISTANT: "I explore advanced topics and cutting-edge features.",
            AgentRole.PRACTICE_FACILITATOR: "I create exercises and hands-on learning activities.",
            AgentRole.PROJECT_GUIDE: "I guide you through building real-world projects.",
            AgentRole.MENTOR: "I provide personalized guidance and learning strategy advice.",
            AgentRole.ASSESSMENT_AGENT: "I evaluate your progress and provide assessments.",
            AgentRole.PROGRESS_TRACKER: "I track your learning progress and milestones.",
            AgentRole.MOTIVATIONAL_COACH: "I provide encouragement and motivation.",
            AgentRole.TROUBLESHOOTER: "I help debug issues and solve technical problems.",
            AgentRole.KNOWLEDGE_SYNTHESIZER: "I synthesize information from multiple sources.",
        }
        return descriptions.get(self.agent_role, "I'm here to help with your learning journey.")
    
    def __str__(self) -> str:
        """String representation of the agent."""
        return f"{self.agent_role.value.title().replace('_', ' ')} Agent"
    
    def __repr__(self) -> str:
        """Detailed string representation."""
        return f"<{self.__class__.__name__}(role={self.agent_role.value})>"
