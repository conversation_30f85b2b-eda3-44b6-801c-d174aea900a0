# LLM API Keys - At least one is required
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_API_KEY=your_google_gemini_api_key_here

# LangSmith Configuration (Optional - for tracing and monitoring)
LANGCHAIN_TRACING_V2=true
LANGCHAIN_API_KEY=your_langsmith_api_key_here
LANGCHAIN_PROJECT=gaapf-guidance-ai-agent

# Database Configuration (Optional - for advanced features)
REDIS_URL=redis://localhost:6379/0
POSTGRES_URL=postgresql://user:password@localhost:5432/pyframeworks_db

# System Configuration
LOG_LEVEL=INFO
DEBUG_MODE=false

# Constellation Configuration
MAX_CONCURRENT_AGENTS=16
CONSTELLATION_TIMEOUT=300
ROLE_MORPHING_ENABLED=true

# Temporal Optimization Configuration
EFFECTIVENESS_TRACKING_ENABLED=true
PATTERN_ANALYSIS_ENABLED=true
OPTIMIZATION_AUTO_APPLY=false

# Memory Management Configuration
MAX_MEMORY_SESSIONS=1000
MEMORY_CLEANUP_INTERVAL=3600

# Interface Configuration
CLI_ENABLED=true
WEB_ENABLED=true
API_ENABLED=true
API_HOST=0.0.0.0
API_PORT=8000

# Learning Configuration
DEFAULT_FRAMEWORK=langchain
DEFAULT_CONSTELLATION_TYPE=theory_practice_balanced
DEFAULT_LEARNING_PACE=moderate
