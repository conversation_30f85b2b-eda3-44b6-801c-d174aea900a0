"""
FastAPI REST API for GAAPF.

This module provides a REST API interface for GAAPF functionality,
enabling integration with other applications and services.
"""

import sys
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# Add src to path for imports
src_path = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(src_path))

from pyframeworks_assistant.config.user_profiles import UserProfile, SkillLevel, LearningPace, LearningStyle
from pyframeworks_assistant.config.framework_configs import SupportedFrameworks, get_available_frameworks
from pyframeworks_assistant.config.constellation_configs import ConstellationType, get_available_constellations
from pyframeworks_assistant.core.constellation import ConstellationManager
from pyframeworks_assistant.core.temporal_state import TemporalStateManager
from pyframeworks_assistant.core.learning_hub import LearningHub

# API Models
class UserProfileCreate(BaseModel):
    """Request model for creating user profiles."""
    user_id: str
    name: Optional[str] = None
    programming_experience_years: int = 0
    python_skill_level: str = "beginner"
    learning_pace: str = "moderate"
    preferred_learning_style: str = "mixed"
    learning_goals: List[str] = []

class LearningSessionStart(BaseModel):
    """Request model for starting learning sessions."""
    user_id: str
    framework: str = "langchain"
    module_id: Optional[str] = None
    constellation_type: Optional[str] = None

class LearningInteraction(BaseModel):
    """Request model for learning interactions."""
    session_id: str
    user_message: str
    user_id: str

class SessionFeedback(BaseModel):
    """Request model for session feedback."""
    session_id: str
    satisfaction: Optional[float] = None
    comprehension: Optional[float] = None

# Initialize FastAPI app
app = FastAPI(
    title="GAAPF API",
    description="Guidance AI Agent for Python Framework - REST API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global components (in production, these would be properly managed)
constellation_manager = None
temporal_manager = None
learning_hub = None

@app.on_event("startup")
async def startup_event():
    """Initialize GAAPF components on startup."""
    global constellation_manager, temporal_manager, learning_hub
    
    # Initialize core components
    constellation_manager = ConstellationManager(llm_client=None)  # Mock LLM client
    temporal_manager = TemporalStateManager()
    learning_hub = LearningHub(constellation_manager, temporal_manager)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0"
    }

# System information endpoints
@app.get("/api/v1/system/info")
async def get_system_info():
    """Get system information."""
    return {
        "name": "GAAPF - Guidance AI Agent for Python Framework",
        "version": "1.0.0",
        "description": "An Adaptive Multi-Agent Learning System for AI Framework Education",
        "supported_frameworks": [f.value for f in get_available_frameworks()],
        "constellation_types": [c.value for c in get_available_constellations()],
        "features": [
            "Adaptive Learning Constellations",
            "Temporal Optimization",
            "Multi-Agent Intelligence",
            "Personalized Learning Paths"
        ]
    }

@app.get("/api/v1/frameworks")
async def get_frameworks():
    """Get available frameworks."""
    frameworks = []
    for framework in get_available_frameworks():
        from pyframeworks_assistant.config.framework_configs import get_framework_config
        config = get_framework_config(framework)
        frameworks.append({
            "id": framework.value,
            "name": config.display_name,
            "description": config.description,
            "version": config.version,
            "modules": len(config.modules),
            "estimated_hours": config.total_estimated_hours,
            "supported": config.is_fully_supported
        })
    return {"frameworks": frameworks}

@app.get("/api/v1/constellations")
async def get_constellations():
    """Get available constellation types."""
    constellations = []
    for constellation_type in get_available_constellations():
        from pyframeworks_assistant.config.constellation_configs import get_constellation_config
        config = get_constellation_config(constellation_type)
        constellations.append({
            "id": constellation_type.value,
            "name": config.display_name,
            "description": config.description,
            "focus": config.focus_area,
            "primary_agents": [agent.value for agent in config.primary_agents],
            "support_agents": [agent.value for agent in config.support_agents],
            "max_agents": config.max_concurrent_agents
        })
    return {"constellations": constellations}

# User management endpoints
@app.post("/api/v1/users")
async def create_user_profile(profile_data: UserProfileCreate):
    """Create a new user profile."""
    try:
        profile = UserProfile(
            user_id=profile_data.user_id,
            name=profile_data.name,
            programming_experience_years=profile_data.programming_experience_years,
            python_skill_level=SkillLevel(profile_data.python_skill_level),
            learning_pace=LearningPace(profile_data.learning_pace),
            preferred_learning_style=LearningStyle(profile_data.preferred_learning_style),
            learning_goals=profile_data.learning_goals
        )
        
        return {
            "message": "User profile created successfully",
            "user_id": profile.user_id,
            "profile": {
                "name": profile.name,
                "experience_level": profile.get_experience_level(),
                "python_skill": profile.python_skill_level.value,
                "learning_style": profile.preferred_learning_style.value,
                "goals": profile.learning_goals
            }
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/api/v1/users/{user_id}")
async def get_user_profile(user_id: str):
    """Get user profile information."""
    # In a real implementation, this would load from storage
    return {
        "user_id": user_id,
        "message": "User profile retrieval not implemented in demo",
        "note": "In the full system, this would return complete user profile data"
    }

# Learning session endpoints
@app.post("/api/v1/sessions/start")
async def start_learning_session(session_data: LearningSessionStart):
    """Start a new learning session."""
    try:
        # Create a mock user profile for demo
        profile = UserProfile(
            user_id=session_data.user_id,
            python_skill_level=SkillLevel.INTERMEDIATE,
            preferred_learning_style=LearningStyle.MIXED
        )
        
        framework = SupportedFrameworks(session_data.framework)
        constellation_type = ConstellationType(session_data.constellation_type) if session_data.constellation_type else None
        
        session_info = await learning_hub.start_learning_session(
            profile, framework, session_data.module_id, constellation_type
        )
        
        return {
            "message": "Learning session started successfully",
            "session_info": session_info
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/api/v1/sessions/interact")
async def process_interaction(interaction: LearningInteraction):
    """Process a learning interaction."""
    try:
        # Create a mock user profile for demo
        profile = UserProfile(
            user_id=interaction.user_id,
            python_skill_level=SkillLevel.INTERMEDIATE
        )
        
        result = await learning_hub.process_learning_interaction(
            interaction.session_id,
            interaction.user_message,
            profile
        )
        
        return {
            "message": "Interaction processed successfully",
            "result": result
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/api/v1/sessions/{session_id}/end")
async def end_learning_session(session_id: str, feedback: Optional[SessionFeedback] = None):
    """End a learning session."""
    try:
        feedback_data = {}
        if feedback:
            if feedback.satisfaction is not None:
                feedback_data["satisfaction"] = feedback.satisfaction
            if feedback.comprehension is not None:
                feedback_data["comprehension"] = feedback.comprehension
        
        summary = await learning_hub.end_learning_session(session_id, feedback_data)
        
        return {
            "message": "Learning session ended successfully",
            "summary": summary
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/api/v1/sessions/{session_id}/status")
async def get_session_status(session_id: str):
    """Get session status information."""
    try:
        status = constellation_manager.get_constellation_status(session_id)
        if not status:
            raise HTTPException(status_code=404, detail="Session not found")
        
        return {
            "session_id": session_id,
            "status": status
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

# Analytics endpoints
@app.get("/api/v1/analytics/user/{user_id}")
async def get_user_analytics(user_id: str, days: int = 30):
    """Get user learning analytics."""
    analytics = temporal_manager.get_user_learning_analytics(user_id)
    
    return {
        "user_id": user_id,
        "time_period_days": days,
        "analytics": analytics
    }

@app.get("/api/v1/analytics/system")
async def get_system_analytics(days: int = 7):
    """Get system-wide analytics."""
    return {
        "message": "System analytics not implemented in demo",
        "note": "In the full system, this would return comprehensive system metrics",
        "time_period_days": days
    }

# Demo endpoints
@app.get("/api/v1/demo/chat")
async def demo_chat_response(message: str):
    """Demo chat endpoint with mock responses."""
    
    # Simple mock response based on message content
    message_lower = message.lower()
    
    if "langchain" in message_lower:
        response = {
            "agent": "instructor",
            "content": "LangChain is a framework for developing applications powered by language models. It provides tools for prompt management, chains, agents, and memory.",
            "type": "explanation",
            "includes_code": False
        }
    elif "example" in message_lower or "code" in message_lower:
        response = {
            "agent": "code_assistant", 
            "content": "Here's a simple LangChain example:\n\n```python\nfrom langchain.llms import OpenAI\nllm = OpenAI()\nresult = llm('Explain quantum computing')\nprint(result)\n```",
            "type": "code_example",
            "includes_code": True
        }
    else:
        response = {
            "agent": "instructor",
            "content": f"I understand you're asking about: '{message}'. This is a demo response. In the full system, I would provide personalized, context-aware assistance.",
            "type": "general",
            "includes_code": False
        }
    
    return {
        "message": "Demo response generated",
        "user_message": message,
        "response": response,
        "timestamp": datetime.utcnow().isoformat()
    }

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request, exc):
    return {"error": "Endpoint not found", "detail": str(exc)}

@app.exception_handler(500)
async def internal_error_handler(request, exc):
    return {"error": "Internal server error", "detail": "An unexpected error occurred"}

def run_api(host: str = "0.0.0.0", port: int = 8000, reload: bool = False):
    """Run the FastAPI application."""
    uvicorn.run(
        "pyframeworks_assistant.interfaces.api.main:app",
        host=host,
        port=port,
        reload=reload,
        log_level="info"
    )

if __name__ == "__main__":
    run_api(reload=True)
