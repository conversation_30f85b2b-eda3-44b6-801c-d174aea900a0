"""
Learning hub for coordinating educational content and progress tracking.

This module provides centralized learning coordination, content management,
and progress tracking across different frameworks and modules.
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from pydantic import BaseModel, Field

from ..config.user_profiles import UserProfile
from ..config.framework_configs import (
    SupportedFrameworks, 
    FrameworkConfig,
    LearningModule,
    get_framework_config,
    get_module_by_id
)
from ..config.constellation_configs import ConstellationType
from .constellation import ConstellationManager
from .temporal_state import TemporalStateManager, LearningSession, LearningMetrics


class LearningPath(BaseModel):
    """Represents a personalized learning path for a user."""
    
    user_id: str = Field(..., description="User identifier")
    framework: SupportedFrameworks = Field(..., description="Target framework")
    
    # Path Configuration
    recommended_modules: List[str] = Field(default_factory=list, description="Recommended module sequence")
    current_module: Optional[str] = Field(None, description="Currently active module")
    completed_modules: List[str] = Field(default_factory=list, description="Completed modules")
    
    # Progress Tracking
    estimated_completion_hours: float = Field(0.0, description="Estimated total hours")
    actual_hours_spent: float = Field(0.0, description="Actual hours spent")
    completion_percentage: float = Field(0.0, description="Overall completion percentage")
    
    # Personalization
    difficulty_adjustment: float = Field(1.0, description="Difficulty multiplier")
    pace_adjustment: float = Field(1.0, description="Pace multiplier")
    preferred_constellation: Optional[ConstellationType] = Field(None, description="Preferred constellation type")
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    last_updated: datetime = Field(default_factory=datetime.utcnow)
    
    class Config:
        use_enum_values = True
    
    def update_progress(self, module_id: str, hours_spent: float) -> None:
        """Update learning progress."""
        if module_id not in self.completed_modules:
            self.completed_modules.append(module_id)
        
        self.actual_hours_spent += hours_spent
        self.completion_percentage = len(self.completed_modules) / max(1, len(self.recommended_modules)) * 100
        self.last_updated = datetime.utcnow()
    
    def get_next_module(self) -> Optional[str]:
        """Get the next recommended module."""
        for module_id in self.recommended_modules:
            if module_id not in self.completed_modules:
                return module_id
        return None


class LearningHub:
    """
    Central hub for learning coordination and progress management.
    
    This class coordinates between constellation management, temporal optimization,
    and learning content delivery to provide a cohesive learning experience.
    """
    
    def __init__(
        self,
        constellation_manager: ConstellationManager,
        temporal_manager: TemporalStateManager,
    ):
        """Initialize learning hub."""
        self.constellation_manager = constellation_manager
        self.temporal_manager = temporal_manager
        
        # Learning state
        self.active_learning_paths: Dict[str, LearningPath] = {}
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
    
    async def start_learning_session(
        self,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: Optional[str] = None,
        constellation_type: Optional[ConstellationType] = None,
    ) -> Dict[str, Any]:
        """Start a new learning session."""
        
        # Generate session ID
        session_id = f"{user_profile.user_id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        
        # Get or create learning path
        learning_path = await self._get_or_create_learning_path(user_profile, framework)
        
        # Determine module
        if not module_id:
            module_id = learning_path.get_next_module()
            if not module_id:
                # Start with first module of framework
                framework_config = get_framework_config(framework)
                if framework_config.modules:
                    module_id = framework_config.modules[0].module_id
                else:
                    raise ValueError(f"No modules available for framework {framework}")
        
        # Optimize constellation selection
        if not constellation_type:
            constellation_type, confidence = await self.temporal_manager.optimize_constellation_selection(
                user_profile, framework, module_id, {}
            )
        
        # Create constellation
        constellation = await self.constellation_manager.create_constellation(
            constellation_type, user_profile, framework, module_id, session_id
        )
        
        # Initialize session state
        session_state = {
            "session_id": session_id,
            "user_id": user_profile.user_id,
            "framework": framework,
            "module_id": module_id,
            "constellation_type": constellation_type,
            "constellation_id": constellation.constellation_id,
            "start_time": datetime.utcnow(),
            "interactions": 0,
            "learning_path": learning_path,
        }
        
        self.active_sessions[session_id] = session_state
        
        # Get module information
        module = get_module_by_id(module_id)
        module_info = module.dict() if module else {"title": "Unknown Module", "description": ""}
        
        return {
            "session_id": session_id,
            "constellation_type": constellation_type,
            "primary_agent": constellation.primary_agent,
            "module_info": module_info,
            "learning_path_progress": {
                "current_module": learning_path.current_module,
                "completion_percentage": learning_path.completion_percentage,
                "modules_completed": len(learning_path.completed_modules),
                "total_modules": len(learning_path.recommended_modules),
            },
            "welcome_message": await self._generate_welcome_message(
                user_profile, framework, module_info, constellation_type
            ),
        }
    
    async def process_learning_interaction(
        self,
        session_id: str,
        user_message: str,
        user_profile: UserProfile,
    ) -> Dict[str, Any]:
        """Process a learning interaction within a session."""
        
        # Get session state
        session_state = self.active_sessions.get(session_id)
        if not session_state:
            raise ValueError(f"Session {session_id} not found")
        
        # Extract session info
        framework = SupportedFrameworks(session_state["framework"])
        module_id = session_state["module_id"]
        
        # Process through constellation
        response = await self.constellation_manager.run_session(
            session_id, user_message, user_profile, framework, module_id
        )
        
        # Update session state
        session_state["interactions"] += 1
        session_state["last_interaction"] = datetime.utcnow()
        
        # Calculate engagement metrics
        engagement_metrics = self._calculate_engagement_metrics(session_state, response)
        
        return {
            "response": response.dict(),
            "session_info": {
                "interactions": session_state["interactions"],
                "duration_minutes": (
                    datetime.utcnow() - session_state["start_time"]
                ).total_seconds() / 60,
                "current_agent": response.agent_role,
            },
            "engagement_metrics": engagement_metrics,
            "learning_suggestions": await self._generate_learning_suggestions(
                user_profile, session_state, response
            ),
        }
    
    async def end_learning_session(
        self,
        session_id: str,
        user_feedback: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """End a learning session and record metrics."""
        
        # Get session state
        session_state = self.active_sessions.get(session_id)
        if not session_state:
            raise ValueError(f"Session {session_id} not found")
        
        # Calculate session metrics
        end_time = datetime.utcnow()
        duration_minutes = (end_time - session_state["start_time"]).total_seconds() / 60
        
        # Get constellation info
        constellation_status = self.constellation_manager.get_constellation_status(session_id)
        
        # Create learning metrics
        metrics = LearningMetrics(
            comprehension_score=user_feedback.get("comprehension", 0.7) if user_feedback else 0.7,
            engagement_score=min(1.0, session_state["interactions"] / 10.0),  # Normalize interactions
            completion_rate=1.0 if duration_minutes >= 10 else duration_minutes / 10.0,
            satisfaction_score=user_feedback.get("satisfaction", 0.8) if user_feedback else 0.8,
            time_efficiency=min(1.0, session_state["interactions"] / max(1, duration_minutes / 5)),
        )
        
        # Create session record
        learning_session = LearningSession(
            session_id=session_id,
            user_id=session_state["user_id"],
            constellation_type=ConstellationType(session_state["constellation_type"]),
            framework=SupportedFrameworks(session_state["framework"]),
            module_id=session_state["module_id"],
            start_time=session_state["start_time"],
            end_time=end_time,
            duration_minutes=duration_minutes,
            total_interactions=session_state["interactions"],
            agent_handoffs=constellation_status["handoff_count"] if constellation_status else 0,
            primary_agents_used=[constellation_status["primary_agent"]] if constellation_status else [],
            metrics=metrics,
            user_skill_level=session_state.get("user_skill_level", "intermediate"),
            learning_style=session_state.get("learning_style", "mixed"),
            learning_pace=session_state.get("learning_pace", "moderate"),
        )
        
        # Record session in temporal manager
        await self.temporal_manager.record_session(learning_session)
        
        # Update learning path
        learning_path = session_state["learning_path"]
        learning_path.update_progress(session_state["module_id"], duration_minutes / 60.0)
        
        # Clean up session
        del self.active_sessions[session_id]
        
        # Generate session summary
        summary = {
            "session_summary": {
                "duration_minutes": round(duration_minutes, 1),
                "interactions": session_state["interactions"],
                "effectiveness_score": round(metrics.calculate_overall_effectiveness(), 3),
                "constellation_used": session_state["constellation_type"],
                "handoffs": constellation_status["handoff_count"] if constellation_status else 0,
            },
            "learning_progress": {
                "module_completed": session_state["module_id"],
                "total_completion": round(learning_path.completion_percentage, 1),
                "next_module": learning_path.get_next_module(),
                "hours_invested": round(learning_path.actual_hours_spent, 1),
            },
            "recommendations": await self._generate_session_recommendations(learning_session),
        }
        
        return summary
    
    async def _get_or_create_learning_path(
        self,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
    ) -> LearningPath:
        """Get existing or create new learning path."""
        
        path_key = f"{user_profile.user_id}_{framework.value}"
        
        if path_key not in self.active_learning_paths:
            # Create new learning path
            framework_config = get_framework_config(framework)
            
            # Determine module sequence based on user profile
            recommended_modules = []
            for module in framework_config.modules:
                # Check prerequisites
                if not module.prerequisites or all(
                    prereq in user_profile.completed_modules for prereq in module.prerequisites
                ):
                    recommended_modules.append(module.module_id)
            
            # Sort by difficulty and prerequisites
            recommended_modules.sort(key=lambda m: len(get_module_by_id(m).prerequisites or []))
            
            learning_path = LearningPath(
                user_id=user_profile.user_id,
                framework=framework,
                recommended_modules=recommended_modules,
                estimated_completion_hours=framework_config.total_estimated_hours,
                completed_modules=user_profile.completed_modules.copy(),
            )
            
            self.active_learning_paths[path_key] = learning_path
        
        return self.active_learning_paths[path_key]
    
    def _calculate_engagement_metrics(
        self,
        session_state: Dict[str, Any],
        response: Any,
    ) -> Dict[str, Any]:
        """Calculate real-time engagement metrics."""
        
        duration_minutes = (
            datetime.utcnow() - session_state["start_time"]
        ).total_seconds() / 60
        
        return {
            "interaction_rate": session_state["interactions"] / max(1, duration_minutes / 5),
            "session_momentum": "high" if session_state["interactions"] > duration_minutes / 2 else "moderate",
            "content_complexity": response.difficulty_level if hasattr(response, 'difficulty_level') else "intermediate",
            "engagement_level": "high" if session_state["interactions"] > 5 else "building",
        }
    
    async def _generate_welcome_message(
        self,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_info: Dict[str, Any],
        constellation_type: ConstellationType,
    ) -> str:
        """Generate personalized welcome message."""
        
        framework_name = framework.value.title()
        module_title = module_info.get("title", "Learning Module")
        constellation_name = constellation_type.value.replace("_", " ").title()
        
        return f"""Welcome to your {framework_name} learning session! 🚀

📚 **Current Module**: {module_title}
🤖 **Learning Constellation**: {constellation_name}
👤 **Personalized for**: {user_profile.get_experience_level()} level

I'm here to guide you through this learning journey. Feel free to ask questions, request examples, or let me know what you'd like to explore first!

What would you like to learn about today?"""
    
    async def _generate_learning_suggestions(
        self,
        user_profile: UserProfile,
        session_state: Dict[str, Any],
        response: Any,
    ) -> List[str]:
        """Generate contextual learning suggestions."""
        
        suggestions = []
        
        # Based on interaction count
        if session_state["interactions"] < 3:
            suggestions.append("Ask for a practical example to see the concept in action")
            suggestions.append("Request a step-by-step explanation if something is unclear")
        
        # Based on response type
        if hasattr(response, 'includes_code_example') and response.includes_code_example:
            suggestions.append("Try modifying the code example to test your understanding")
            suggestions.append("Ask about potential errors or edge cases")
        
        # Based on user profile
        if user_profile.preferred_learning_style.value == "hands_on":
            suggestions.append("Request a hands-on exercise to practice")
        elif user_profile.preferred_learning_style.value == "visual":
            suggestions.append("Ask for a diagram or visual explanation")
        
        return suggestions[:3]  # Limit to top 3 suggestions
    
    async def _generate_session_recommendations(
        self,
        session: LearningSession,
    ) -> List[str]:
        """Generate post-session recommendations."""
        
        recommendations = []
        effectiveness = session.metrics.calculate_overall_effectiveness()
        
        if effectiveness > 0.8:
            recommendations.append("Excellent session! Consider tackling more advanced topics.")
            recommendations.append("You're ready to move to the next module.")
        elif effectiveness > 0.6:
            recommendations.append("Good progress! Review key concepts before moving forward.")
            recommendations.append("Consider additional practice exercises.")
        else:
            recommendations.append("Take time to review the material covered today.")
            recommendations.append("Consider a different learning approach or constellation type.")
        
        # Duration-based recommendations
        if session.duration_minutes < 15:
            recommendations.append("Try longer sessions for deeper learning.")
        elif session.duration_minutes > 60:
            recommendations.append("Consider shorter, more focused sessions.")
        
        return recommendations[:3]
