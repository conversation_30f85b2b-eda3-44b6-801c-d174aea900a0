# GAAPF CLI Guide

Complete guide to using the GAAPF Command Line Interface for interactive AI-powered learning.

## 🚀 Quick Start

### Prerequisites

1. **Python 3.10+** installed on your system
2. **At least one LLM API key** (recommended: Google Gemini for free tier)
3. **Git** for cloning the repository

### Installation

```bash
# Clone the repository
git clone https://github.com/your-username/gaapf-guidance-ai-agent.git
cd gaapf-guidance-ai-agent

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env with your API keys
```

### Configuration

Edit the `.env` file with your API keys:

```env
# At least one is required
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_API_KEY=your_google_gemini_api_key_here

# Optional: <PERSON><PERSON><PERSON> for tracing
LANGCHAIN_TRACING_V2=true
LANGCHAIN_API_KEY=your_langsmith_api_key_here
```

### Running the CLI

```bash
python run_cli.py
```

## 🎯 CLI Features

### Real LLM Integration
- **Actual AI responses** from GPT, Claude, or Gemini
- **Intelligent agent selection** based on your questions
- **Context-aware conversations** that remember your learning journey

### Adaptive Learning
- **Personalized constellations** that adapt to your style
- **Progress tracking** across sessions
- **Temporal optimization** that improves over time

### Interactive Experience
- **Natural conversation** with specialized AI agents
- **Seamless agent handoffs** for optimal learning
- **Rich terminal interface** with progress indicators

## 📋 CLI Workflow

### 1. Profile Creation

When you first run the CLI, you'll create your learning profile:

```
What's your name? John Doe
Choose a username: john_doe
How many years of programming experience do you have? 3
Your Python skill level: intermediate
Your AI/ML experience level: beginner
Preferred learning pace: moderate
Preferred learning style: hands_on
What are your learning goals? Learn LangChain, Build chatbots
```

### 2. Framework Selection

Choose which framework to learn:

```
Available Frameworks:
1. LangChain - Framework for developing applications powered by language models
2. LangGraph - Library for building stateful, multi-actor applications

Select framework (1-2): 1
```

### 3. Learning Session

The system will:
- **Analyze your profile** to select optimal constellation
- **Initialize specialized agents** for your learning style
- **Start interactive session** with personalized welcome

```
✅ Session Started!
🆔 Session ID: john_doe_20241201_143022
⭐ Constellation: Hands-On Focused
🤖 Primary Agent: Code Assistant

📖 Current Module: LangChain Fundamentals
📝 Description: Learn the core concepts and basic usage of LangChain

Welcome to your LangChain learning session! 🚀
I'm here to guide you through this learning journey...

You: What is LangChain and how do I get started?
```

### 4. Interactive Learning

Natural conversation with AI agents:

```
🤖 Instructor: LangChain is a powerful framework for building applications 
with Large Language Models. Here are the key concepts:

## Core Components:
1. **LLMs and Chat Models** - Interface with language models
2. **Prompts** - Templates for structuring inputs
3. **Chains** - Combine multiple components
...

💡 Learning Suggestions:
1. Ask for a practical example to see the concept in action
2. Request a step-by-step explanation if something is unclear
3. Try modifying the code example to test your understanding

You: Show me a code example
```

### 5. Agent Handoffs

Intelligent transitions between specialized agents:

```
🤖 Code Assistant: Here's a practical LangChain example:

```python
from langchain.llms import OpenAI
from langchain.chains import LLMChain
from langchain.prompts import PromptTemplate

# Initialize the LLM
llm = OpenAI(temperature=0.7)
...
```

You: I want to practice this
🤖 Practice Facilitator: Great! Let's create some hands-on exercises...
```

## 🎮 CLI Commands

### During Learning Sessions

| Command | Description |
|---------|-------------|
| `help` | Show available commands and tips |
| `status` | Display current session status |
| `adapt` | Show adaptation recommendations |
| `quit` / `exit` / `bye` | End the current session |

### Natural Language

Just type your questions naturally:

- "What is LangChain?"
- "Show me a code example"
- "I want to practice"
- "How do I build a chatbot?"
- "Explain chains in detail"
- "I'm having trouble with this error..."

## 🔧 Advanced Features

### Constellation Adaptation

The system automatically adapts based on your:
- **Learning patterns** and effectiveness
- **Question types** and preferences  
- **Session feedback** and engagement
- **Historical performance** data

### Progress Tracking

Monitor your learning journey:
- **Session summaries** with effectiveness scores
- **Module completion** tracking
- **Learning streaks** and consistency
- **Personalized recommendations**

### Session Analytics

At the end of each session:

```
✅ Session Completed!
⏱️ Duration: 45.2 minutes
💬 Interactions: 23
📊 Effectiveness: 85.3%
🔄 Agent Handoffs: 4

📈 Learning Progress:
✅ Module Completed: LangChain Fundamentals
📊 Overall Progress: 33.3%
⏰ Total Hours: 12.5
➡️ Next Module: Advanced LangChain Features

💡 Recommendations:
• Excellent session! Consider tackling more advanced topics
• You're ready to move to the next module
• Try building a small project to apply what you've learned
```

## 🛠️ Troubleshooting

### Common Issues

**"No API key found"**
- Ensure you've set up your `.env` file correctly
- Check that at least one LLM API key is configured
- Verify the API key format is correct

**"Import errors"**
- Run `pip install -r requirements.txt` to install dependencies
- Ensure you're using Python 3.10 or higher
- Check that you're in the correct directory

**"Session not responding"**
- Check your internet connection
- Verify your API key is valid and has credits
- Try restarting the session

### Getting Help

1. **Use the `help` command** during sessions
2. **Check the logs** for detailed error messages
3. **Review the documentation** in the repository
4. **Open an issue** on GitHub for bugs or feature requests

## 🎯 Learning Tips

### Maximize Your Experience

1. **Be specific** in your questions for better responses
2. **Ask for examples** when learning new concepts
3. **Request practice exercises** to reinforce learning
4. **Provide feedback** to help the system adapt
5. **Take breaks** between intensive sessions

### Effective Learning Patterns

- **Start with basics** before moving to advanced topics
- **Practice immediately** after learning new concepts
- **Ask follow-up questions** to deepen understanding
- **Build small projects** to apply knowledge
- **Review previous sessions** to reinforce learning

## 📊 Understanding Your Data

### What Gets Tracked

- **Learning effectiveness** and comprehension
- **Engagement patterns** and preferences
- **Session duration** and interaction frequency
- **Constellation performance** for your profile
- **Progress through modules** and frameworks

### Privacy and Storage

- **Local storage** by default (no cloud sync)
- **User profiles** stored in `data/user_profiles/`
- **Session data** in `data/temporal_state/`
- **No sensitive data** transmitted to external services
- **API keys** remain local and secure

## 🚀 Next Steps

After mastering the CLI:

1. **Explore the Web Interface** with `streamlit run src/pyframeworks_assistant/interfaces/web/streamlit_app.py`
2. **Try the API** for custom integrations
3. **Contribute** to the project on GitHub
4. **Share feedback** to help improve GAAPF
5. **Build amazing AI applications** with your new skills!

---

**Happy Learning with GAAPF! 🤖📚**
