"""
Constellation management system for adaptive agent teams.

This module implements the core constellation formation and management logic,
including agent selection, coordination, and intelligent handoff mechanisms.
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum

from ..config.user_profiles import UserProfile
from ..config.framework_configs import SupportedFrameworks
from ..config.constellation_configs import (
    ConstellationType, 
    ConstellationConfig, 
    AgentRole,
    get_constellation_config,
    get_optimal_constellation_for_profile
)
from ..agents.base_agent import BaseAgent, AgentResponse, HandoffSuggestion
from ..agents.knowledge_agents import (
    InstructorAgent, DocumentationExpertAgent, 
    ResearchAssistantAgent, KnowledgeSynthesizerAgent
)
from ..agents.practice_agents import (
    CodeAssistantAgent, PracticeFacilitatorAgent,
    ProjectGuideAgent, TroubleshooterAgent
)
from ..agents.support_agents import MentorAgent, MotivationalCoachAgent
from ..agents.assessment_agents import AssessmentAgent, ProgressTrackerAgent


class ConstellationStatus(str, Enum):
    """Status of a constellation."""
    INITIALIZING = "initializing"
    ACTIVE = "active"
    TRANSITIONING = "transitioning"
    PAUSED = "paused"
    COMPLETED = "completed"
    ERROR = "error"


class ActiveConstellation(BaseModel):
    """Represents an active agent constellation."""
    
    constellation_id: str = Field(..., description="Unique constellation identifier")
    constellation_type: ConstellationType = Field(..., description="Type of constellation")
    session_id: str = Field(..., description="Associated session ID")
    user_id: str = Field(..., description="User ID")
    
    # Agent Management
    active_agents: Dict[AgentRole, BaseAgent] = Field(default_factory=dict, description="Active agents")
    primary_agent: Optional[AgentRole] = Field(None, description="Currently primary agent")
    
    # Configuration
    config: ConstellationConfig = Field(..., description="Constellation configuration")
    max_concurrent_agents: int = Field(3, description="Maximum concurrent agents")
    
    # State Management
    status: ConstellationStatus = Field(ConstellationStatus.INITIALIZING, description="Constellation status")
    context_data: Dict[str, Any] = Field(default_factory=dict, description="Shared context data")
    conversation_history: List[Dict[str, Any]] = Field(default_factory=list, description="Conversation history")
    
    # Metrics
    created_at: datetime = Field(default_factory=datetime.utcnow)
    last_activity_at: datetime = Field(default_factory=datetime.utcnow)
    total_interactions: int = Field(0, description="Total user interactions")
    handoff_count: int = Field(0, description="Number of agent handoffs")
    
    class Config:
        arbitrary_types_allowed = True
        use_enum_values = True
    
    def update_activity(self) -> None:
        """Update last activity timestamp."""
        self.last_activity_at = datetime.utcnow()
        self.total_interactions += 1
    
    def add_agent(self, agent_role: AgentRole, agent: BaseAgent) -> None:
        """Add an agent to the constellation."""
        self.active_agents[agent_role] = agent
        if self.primary_agent is None:
            self.primary_agent = agent_role
    
    def get_primary_agent(self) -> Optional[BaseAgent]:
        """Get the primary agent."""
        if self.primary_agent and self.primary_agent in self.active_agents:
            return self.active_agents[self.primary_agent]
        return None
    
    def set_primary_agent(self, agent_role: AgentRole) -> bool:
        """Set the primary agent."""
        if agent_role in self.active_agents:
            self.primary_agent = agent_role
            return True
        return False
    
    def record_handoff(self) -> None:
        """Record an agent handoff."""
        self.handoff_count += 1
        self.update_activity()


class ConstellationManager:
    """
    Manages the formation and coordination of agent constellations.
    
    This class handles constellation creation, agent coordination,
    intelligent handoffs, and session management.
    """
    
    def __init__(self, llm_client: Any = None):
        """Initialize constellation manager."""
        self.llm_client = llm_client
        self.active_constellations: Dict[str, ActiveConstellation] = {}
        self.agent_factory = self._create_agent_factory()
    
    def _create_agent_factory(self) -> Dict[AgentRole, type]:
        """Create agent factory mapping."""
        return {
            # Knowledge Domain
            AgentRole.INSTRUCTOR: InstructorAgent,
            AgentRole.DOCUMENTATION_EXPERT: DocumentationExpertAgent,
            AgentRole.RESEARCH_ASSISTANT: ResearchAssistantAgent,
            AgentRole.KNOWLEDGE_SYNTHESIZER: KnowledgeSynthesizerAgent,
            
            # Practice Domain
            AgentRole.CODE_ASSISTANT: CodeAssistantAgent,
            AgentRole.PRACTICE_FACILITATOR: PracticeFacilitatorAgent,
            AgentRole.PROJECT_GUIDE: ProjectGuideAgent,
            AgentRole.TROUBLESHOOTER: TroubleshooterAgent,
            
            # Support Domain
            AgentRole.MENTOR: MentorAgent,
            AgentRole.MOTIVATIONAL_COACH: MotivationalCoachAgent,
            
            # Assessment Domain
            AgentRole.ASSESSMENT_AGENT: AssessmentAgent,
            AgentRole.PROGRESS_TRACKER: ProgressTrackerAgent,
        }
    
    async def create_constellation(
        self,
        constellation_type: ConstellationType,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
        session_id: str,
    ) -> ActiveConstellation:
        """Create and initialize a new constellation."""
        
        # Get constellation configuration
        config = get_constellation_config(constellation_type)
        
        # Create constellation instance
        constellation = ActiveConstellation(
            constellation_id=f"{session_id}_{constellation_type.value}",
            constellation_type=constellation_type,
            session_id=session_id,
            user_id=user_profile.user_id,
            config=config,
            max_concurrent_agents=config.max_concurrent_agents,
        )
        
        # Initialize primary agents
        for agent_role in config.primary_agents:
            agent = await self._create_agent(agent_role)
            constellation.add_agent(agent_role, agent)
        
        # Initialize support agents if needed
        for agent_role in config.support_agents:
            if len(constellation.active_agents) < constellation.max_concurrent_agents:
                agent = await self._create_agent(agent_role)
                constellation.add_agent(agent_role, agent)
        
        # Set constellation status
        constellation.status = ConstellationStatus.ACTIVE
        
        # Store constellation
        self.active_constellations[constellation.constellation_id] = constellation
        
        return constellation
    
    async def _create_agent(self, agent_role: AgentRole) -> BaseAgent:
        """Create an agent instance."""
        agent_class = self.agent_factory.get(agent_role)
        if not agent_class:
            raise ValueError(f"Unknown agent role: {agent_role}")
        
        return agent_class(llm_client=self.llm_client)
    
    async def run_session(
        self,
        session_id: str,
        user_message: str,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
    ) -> AgentResponse:
        """Run a learning session with the active constellation."""
        
        # Find active constellation for session
        constellation = self._find_constellation_by_session(session_id)
        if not constellation:
            # Create default constellation if none exists
            optimal_types = get_optimal_constellation_for_profile(
                user_profile.preferred_learning_style.value,
                user_profile.python_skill_level.value,
                user_profile.learning_pace.value
            )
            constellation_type = optimal_types[0] if optimal_types else ConstellationType.THEORY_PRACTICE_BALANCED
            constellation = await self.create_constellation(
                constellation_type, user_profile, framework, module_id, session_id
            )
        
        # Get primary agent
        primary_agent = constellation.get_primary_agent()
        if not primary_agent:
            raise RuntimeError("No primary agent available in constellation")
        
        # Generate response
        response = await primary_agent.generate_response(
            user_message, user_profile, framework, module_id, constellation.context_data
        )
        
        # Update constellation state
        constellation.update_activity()
        constellation.conversation_history.append({
            "user_message": user_message,
            "agent_response": response.dict(),
            "timestamp": datetime.utcnow().isoformat(),
        })
        
        # Handle potential handoffs
        if response.handoff_suggestion:
            await self._handle_handoff(constellation, response.handoff_suggestion)
        
        return response
    
    async def _handle_handoff(
        self,
        constellation: ActiveConstellation,
        handoff_suggestion: HandoffSuggestion,
    ) -> bool:
        """Handle agent handoff within constellation."""
        
        target_role = handoff_suggestion.target_agent
        
        # Check if target agent is already active
        if target_role not in constellation.active_agents:
            # Check if we can add more agents
            if len(constellation.active_agents) >= constellation.max_concurrent_agents:
                # Remove least recently used agent (simple strategy)
                # In production, this would be more sophisticated
                pass
            
            # Create and add target agent
            try:
                target_agent = await self._create_agent(target_role)
                constellation.add_agent(target_role, target_agent)
            except Exception as e:
                # Log error and continue with current agent
                return False
        
        # Set new primary agent
        success = constellation.set_primary_agent(target_role)
        if success:
            constellation.record_handoff()
            
            # Update context with handoff data
            constellation.context_data.update(handoff_suggestion.context_data)
        
        return success
    
    def _find_constellation_by_session(self, session_id: str) -> Optional[ActiveConstellation]:
        """Find constellation by session ID."""
        for constellation in self.active_constellations.values():
            if constellation.session_id == session_id:
                return constellation
        return None
    
    async def get_optimal_constellation_type(
        self,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
        session_context: Dict[str, Any],
    ) -> ConstellationType:
        """Determine optimal constellation type for user and context."""
        
        # Get profile-based recommendations
        optimal_types = get_optimal_constellation_for_profile(
            user_profile.preferred_learning_style.value,
            user_profile.python_skill_level.value,
            user_profile.learning_pace.value
        )
        
        # Apply contextual adjustments
        if session_context.get("needs_debugging"):
            return ConstellationType.PROJECT_ORIENTED
        elif session_context.get("assessment_requested"):
            return ConstellationType.ASSESSMENT_FOCUSED
        elif session_context.get("research_intensive"):
            return ConstellationType.RESEARCH_INTENSIVE
        
        # Return best match or default
        return optimal_types[0] if optimal_types else ConstellationType.THEORY_PRACTICE_BALANCED
    
    def get_constellation_status(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get status information for a constellation."""
        constellation = self._find_constellation_by_session(session_id)
        if not constellation:
            return None
        
        return {
            "constellation_id": constellation.constellation_id,
            "constellation_type": constellation.constellation_type,
            "status": constellation.status,
            "primary_agent": constellation.primary_agent,
            "active_agents": list(constellation.active_agents.keys()),
            "total_interactions": constellation.total_interactions,
            "handoff_count": constellation.handoff_count,
            "created_at": constellation.created_at.isoformat(),
            "last_activity_at": constellation.last_activity_at.isoformat(),
        }
    
    async def cleanup_inactive_constellations(self, max_age_hours: int = 24) -> int:
        """Clean up inactive constellations."""
        current_time = datetime.utcnow()
        cleanup_count = 0
        
        constellations_to_remove = []
        for constellation_id, constellation in self.active_constellations.items():
            age_hours = (current_time - constellation.last_activity_at).total_seconds() / 3600
            if age_hours > max_age_hours:
                constellations_to_remove.append(constellation_id)
        
        for constellation_id in constellations_to_remove:
            del self.active_constellations[constellation_id]
            cleanup_count += 1
        
        return cleanup_count
