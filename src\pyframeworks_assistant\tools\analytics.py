"""
Analytics engine for GAAPF learning insights.

This module provides analytics and reporting capabilities for learning
effectiveness, user engagement, and system performance.
"""

import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from pathlib import Path
from pydantic import BaseModel, Field
from collections import defaultdict

from ..core.temporal_state import LearningSession, LearningMetrics
from ..config.constellation_configs import ConstellationType
from ..config.framework_configs import SupportedFrameworks


class LearningInsight(BaseModel):
    """Individual learning insight or recommendation."""
    
    insight_type: str = Field(..., description="Type of insight")
    title: str = Field(..., description="Insight title")
    description: str = Field(..., description="Detailed description")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence in insight")
    actionable: bool = Field(True, description="Whether insight is actionable")
    priority: str = Field("medium", description="Priority level")
    
    # Supporting data
    supporting_data: Dict[str, Any] = Field(default_factory=dict, description="Supporting metrics")
    recommendations: List[str] = Field(default_factory=list, description="Specific recommendations")
    
    class Config:
        use_enum_values = True


class AnalyticsReport(BaseModel):
    """Comprehensive analytics report."""
    
    report_id: str = Field(..., description="Unique report identifier")
    user_id: Optional[str] = Field(None, description="User ID for user-specific reports")
    report_type: str = Field(..., description="Type of report")
    
    # Report metadata
    generated_at: datetime = Field(default_factory=datetime.utcnow)
    time_period: Dict[str, datetime] = Field(..., description="Time period covered")
    
    # Core metrics
    summary_metrics: Dict[str, Any] = Field(default_factory=dict, description="Summary metrics")
    detailed_metrics: Dict[str, Any] = Field(default_factory=dict, description="Detailed metrics")
    
    # Insights and recommendations
    insights: List[LearningInsight] = Field(default_factory=list, description="Generated insights")
    trends: Dict[str, Any] = Field(default_factory=dict, description="Trend analysis")
    
    # Visualizations (data for charts/graphs)
    visualizations: Dict[str, Any] = Field(default_factory=dict, description="Visualization data")
    
    class Config:
        arbitrary_types_allowed = True


class AnalyticsEngine:
    """
    Analytics engine for generating learning insights and reports.
    
    Analyzes learning data to provide insights about effectiveness,
    engagement patterns, and optimization opportunities.
    """
    
    def __init__(self, data_path: str = "data/analytics"):
        """Initialize analytics engine."""
        self.data_path = Path(data_path)
        self.data_path.mkdir(parents=True, exist_ok=True)
        
        # Cache for processed data
        self.session_cache: List[LearningSession] = []
        self.last_cache_update: Optional[datetime] = None
    
    def load_session_data(self, sessions: List[LearningSession]) -> None:
        """Load session data for analysis."""
        self.session_cache = sessions
        self.last_cache_update = datetime.utcnow()
    
    def generate_user_report(
        self,
        user_id: str,
        sessions: List[LearningSession],
        days_back: int = 30
    ) -> AnalyticsReport:
        """Generate comprehensive user analytics report."""
        
        # Filter sessions for time period
        cutoff_date = datetime.utcnow() - timedelta(days=days_back)
        recent_sessions = [
            s for s in sessions 
            if s.user_id == user_id and s.start_time >= cutoff_date
        ]
        
        if not recent_sessions:
            return self._empty_report(user_id, "user_report")
        
        # Calculate summary metrics
        summary_metrics = self._calculate_summary_metrics(recent_sessions)
        
        # Calculate detailed metrics
        detailed_metrics = self._calculate_detailed_metrics(recent_sessions)
        
        # Generate insights
        insights = self._generate_user_insights(recent_sessions, summary_metrics)
        
        # Analyze trends
        trends = self._analyze_trends(recent_sessions)
        
        # Prepare visualizations
        visualizations = self._prepare_user_visualizations(recent_sessions)
        
        return AnalyticsReport(
            report_id=f"user_{user_id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            user_id=user_id,
            report_type="user_report",
            time_period={
                "start": cutoff_date,
                "end": datetime.utcnow()
            },
            summary_metrics=summary_metrics,
            detailed_metrics=detailed_metrics,
            insights=insights,
            trends=trends,
            visualizations=visualizations
        )
    
    def generate_system_report(
        self,
        sessions: List[LearningSession],
        days_back: int = 7
    ) -> AnalyticsReport:
        """Generate system-wide analytics report."""
        
        cutoff_date = datetime.utcnow() - timedelta(days=days_back)
        recent_sessions = [s for s in sessions if s.start_time >= cutoff_date]
        
        if not recent_sessions:
            return self._empty_report(None, "system_report")
        
        # System-wide metrics
        summary_metrics = self._calculate_system_summary(recent_sessions)
        detailed_metrics = self._calculate_system_detailed(recent_sessions)
        
        # System insights
        insights = self._generate_system_insights(recent_sessions)
        
        # System trends
        trends = self._analyze_system_trends(recent_sessions)
        
        # System visualizations
        visualizations = self._prepare_system_visualizations(recent_sessions)
        
        return AnalyticsReport(
            report_id=f"system_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            report_type="system_report",
            time_period={
                "start": cutoff_date,
                "end": datetime.utcnow()
            },
            summary_metrics=summary_metrics,
            detailed_metrics=detailed_metrics,
            insights=insights,
            trends=trends,
            visualizations=visualizations
        )
    
    def _calculate_summary_metrics(self, sessions: List[LearningSession]) -> Dict[str, Any]:
        """Calculate summary metrics for sessions."""
        
        if not sessions:
            return {}
        
        total_sessions = len(sessions)
        total_time = sum(s.duration_minutes for s in sessions)
        total_interactions = sum(s.total_interactions for s in sessions)
        
        # Calculate average effectiveness
        effectiveness_scores = [s.metrics.calculate_overall_effectiveness() for s in sessions]
        avg_effectiveness = sum(effectiveness_scores) / len(effectiveness_scores)
        
        # Calculate engagement metrics
        engagement_scores = [s.metrics.engagement_score for s in sessions]
        avg_engagement = sum(engagement_scores) / len(engagement_scores)
        
        return {
            "total_sessions": total_sessions,
            "total_learning_hours": round(total_time / 60, 1),
            "average_session_length": round(total_time / total_sessions, 1),
            "total_interactions": total_interactions,
            "average_effectiveness": round(avg_effectiveness, 3),
            "average_engagement": round(avg_engagement, 3),
            "interactions_per_minute": round(total_interactions / max(1, total_time), 2),
        }
    
    def _calculate_detailed_metrics(self, sessions: List[LearningSession]) -> Dict[str, Any]:
        """Calculate detailed metrics for sessions."""
        
        # Constellation performance
        constellation_performance = defaultdict(list)
        for session in sessions:
            effectiveness = session.metrics.calculate_overall_effectiveness()
            constellation_performance[session.constellation_type.value].append(effectiveness)
        
        constellation_avg = {
            k: round(sum(v) / len(v), 3) for k, v in constellation_performance.items()
        }
        
        # Framework progress
        framework_progress = defaultdict(lambda: {"sessions": 0, "time": 0, "effectiveness": []})
        for session in sessions:
            framework = session.framework.value
            framework_progress[framework]["sessions"] += 1
            framework_progress[framework]["time"] += session.duration_minutes
            framework_progress[framework]["effectiveness"].append(
                session.metrics.calculate_overall_effectiveness()
            )
        
        framework_summary = {}
        for framework, data in framework_progress.items():
            framework_summary[framework] = {
                "sessions": data["sessions"],
                "hours": round(data["time"] / 60, 1),
                "avg_effectiveness": round(sum(data["effectiveness"]) / len(data["effectiveness"]), 3)
            }
        
        return {
            "constellation_performance": constellation_avg,
            "framework_progress": framework_summary,
            "session_length_distribution": self._calculate_session_length_distribution(sessions),
            "effectiveness_distribution": self._calculate_effectiveness_distribution(sessions),
        }
    
    def _generate_user_insights(
        self,
        sessions: List[LearningSession],
        summary_metrics: Dict[str, Any]
    ) -> List[LearningInsight]:
        """Generate insights for user performance."""
        
        insights = []
        
        # Effectiveness insight
        avg_effectiveness = summary_metrics.get("average_effectiveness", 0)
        if avg_effectiveness > 0.8:
            insights.append(LearningInsight(
                insight_type="performance",
                title="Excellent Learning Performance",
                description=f"Your average effectiveness score of {avg_effectiveness:.1%} indicates excellent learning outcomes.",
                confidence=0.9,
                recommendations=["Continue with current learning approach", "Consider tackling more advanced topics"]
            ))
        elif avg_effectiveness < 0.6:
            insights.append(LearningInsight(
                insight_type="performance",
                title="Learning Effectiveness Opportunity",
                description=f"Your effectiveness score of {avg_effectiveness:.1%} suggests room for improvement.",
                confidence=0.8,
                recommendations=["Try different constellation types", "Consider shorter, more focused sessions"]
            ))
        
        # Session length insight
        avg_session_length = summary_metrics.get("average_session_length", 0)
        if avg_session_length > 60:
            insights.append(LearningInsight(
                insight_type="optimization",
                title="Session Length Optimization",
                description="Your sessions average over 60 minutes. Shorter sessions might improve focus.",
                confidence=0.7,
                recommendations=["Try 30-45 minute sessions", "Take breaks between topics"]
            ))
        
        # Engagement insight
        avg_engagement = summary_metrics.get("average_engagement", 0)
        if avg_engagement < 0.5:
            insights.append(LearningInsight(
                insight_type="engagement",
                title="Engagement Enhancement Needed",
                description="Low engagement scores suggest the need for more interactive learning.",
                confidence=0.8,
                recommendations=["Try Hands-On Focused constellation", "Request more code examples and exercises"]
            ))
        
        return insights
    
    def _analyze_trends(self, sessions: List[LearningSession]) -> Dict[str, Any]:
        """Analyze trends in learning data."""
        
        if len(sessions) < 3:
            return {"message": "Insufficient data for trend analysis"}
        
        # Sort sessions by time
        sorted_sessions = sorted(sessions, key=lambda s: s.start_time)
        
        # Effectiveness trend
        effectiveness_scores = [s.metrics.calculate_overall_effectiveness() for s in sorted_sessions]
        effectiveness_trend = self._calculate_trend(effectiveness_scores)
        
        # Engagement trend
        engagement_scores = [s.metrics.engagement_score for s in sorted_sessions]
        engagement_trend = self._calculate_trend(engagement_scores)
        
        # Session length trend
        session_lengths = [s.duration_minutes for s in sorted_sessions]
        length_trend = self._calculate_trend(session_lengths)
        
        return {
            "effectiveness_trend": effectiveness_trend,
            "engagement_trend": engagement_trend,
            "session_length_trend": length_trend,
            "overall_momentum": self._assess_overall_momentum(effectiveness_scores, engagement_scores)
        }
    
    def _calculate_trend(self, values: List[float]) -> str:
        """Calculate trend direction for a series of values."""
        
        if len(values) < 3:
            return "insufficient_data"
        
        # Simple trend calculation
        first_half = values[:len(values)//2]
        second_half = values[len(values)//2:]
        
        first_avg = sum(first_half) / len(first_half)
        second_avg = sum(second_half) / len(second_half)
        
        if second_avg > first_avg * 1.1:
            return "improving"
        elif second_avg < first_avg * 0.9:
            return "declining"
        else:
            return "stable"
    
    def _assess_overall_momentum(
        self,
        effectiveness_scores: List[float],
        engagement_scores: List[float]
    ) -> str:
        """Assess overall learning momentum."""
        
        effectiveness_trend = self._calculate_trend(effectiveness_scores)
        engagement_trend = self._calculate_trend(engagement_scores)
        
        if effectiveness_trend == "improving" and engagement_trend == "improving":
            return "strong_positive"
        elif effectiveness_trend == "improving" or engagement_trend == "improving":
            return "positive"
        elif effectiveness_trend == "declining" and engagement_trend == "declining":
            return "concerning"
        elif effectiveness_trend == "declining" or engagement_trend == "declining":
            return "needs_attention"
        else:
            return "stable"
    
    def _prepare_user_visualizations(self, sessions: List[LearningSession]) -> Dict[str, Any]:
        """Prepare visualization data for user reports."""
        
        # Effectiveness over time
        effectiveness_timeline = [
            {
                "date": s.start_time.isoformat(),
                "effectiveness": s.metrics.calculate_overall_effectiveness(),
                "engagement": s.metrics.engagement_score
            }
            for s in sorted(sessions, key=lambda s: s.start_time)
        ]
        
        # Constellation performance
        constellation_data = defaultdict(list)
        for session in sessions:
            constellation_data[session.constellation_type.value].append(
                session.metrics.calculate_overall_effectiveness()
            )
        
        constellation_chart = [
            {
                "constellation": k.replace("_", " ").title(),
                "average_effectiveness": sum(v) / len(v),
                "sessions": len(v)
            }
            for k, v in constellation_data.items()
        ]
        
        return {
            "effectiveness_timeline": effectiveness_timeline,
            "constellation_performance": constellation_chart,
            "session_distribution": self._calculate_session_length_distribution(sessions)
        }
    
    def _calculate_session_length_distribution(self, sessions: List[LearningSession]) -> Dict[str, int]:
        """Calculate distribution of session lengths."""
        
        distribution = {"0-15": 0, "15-30": 0, "30-45": 0, "45-60": 0, "60+": 0}
        
        for session in sessions:
            duration = session.duration_minutes
            if duration <= 15:
                distribution["0-15"] += 1
            elif duration <= 30:
                distribution["15-30"] += 1
            elif duration <= 45:
                distribution["30-45"] += 1
            elif duration <= 60:
                distribution["45-60"] += 1
            else:
                distribution["60+"] += 1
        
        return distribution
    
    def _calculate_effectiveness_distribution(self, sessions: List[LearningSession]) -> Dict[str, int]:
        """Calculate distribution of effectiveness scores."""
        
        distribution = {"Low (0-0.5)": 0, "Medium (0.5-0.7)": 0, "High (0.7-0.85)": 0, "Excellent (0.85+)": 0}
        
        for session in sessions:
            effectiveness = session.metrics.calculate_overall_effectiveness()
            if effectiveness < 0.5:
                distribution["Low (0-0.5)"] += 1
            elif effectiveness < 0.7:
                distribution["Medium (0.5-0.7)"] += 1
            elif effectiveness < 0.85:
                distribution["High (0.7-0.85)"] += 1
            else:
                distribution["Excellent (0.85+)"] += 1
        
        return distribution
    
    def _empty_report(self, user_id: Optional[str], report_type: str) -> AnalyticsReport:
        """Generate empty report when no data is available."""
        
        return AnalyticsReport(
            report_id=f"empty_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            user_id=user_id,
            report_type=report_type,
            time_period={
                "start": datetime.utcnow() - timedelta(days=30),
                "end": datetime.utcnow()
            },
            summary_metrics={"message": "No data available for the specified time period"},
            insights=[
                LearningInsight(
                    insight_type="data",
                    title="No Learning Data",
                    description="No learning sessions found for analysis.",
                    confidence=1.0,
                    recommendations=["Start a learning session to begin tracking progress"]
                )
            ]
        )
    
    # System-wide analytics methods would be similar but aggregate across all users
    def _calculate_system_summary(self, sessions: List[LearningSession]) -> Dict[str, Any]:
        """Calculate system-wide summary metrics."""
        # Implementation similar to user summary but across all users
        return self._calculate_summary_metrics(sessions)
    
    def _calculate_system_detailed(self, sessions: List[LearningSession]) -> Dict[str, Any]:
        """Calculate system-wide detailed metrics."""
        return self._calculate_detailed_metrics(sessions)
    
    def _generate_system_insights(self, sessions: List[LearningSession]) -> List[LearningInsight]:
        """Generate system-wide insights."""
        # Implementation would analyze system performance patterns
        return []
    
    def _analyze_system_trends(self, sessions: List[LearningSession]) -> Dict[str, Any]:
        """Analyze system-wide trends."""
        return self._analyze_trends(sessions)
    
    def _prepare_system_visualizations(self, sessions: List[LearningSession]) -> Dict[str, Any]:
        """Prepare system-wide visualization data."""
        return self._prepare_user_visualizations(sessions)
