"""
Assessment domain agents for the GAAPF constellation system.

This module implements agents specialized in progress evaluation:
- AssessmentAgent: Evaluates learning progress and provides assessments
- ProgressTrackerAgent: Tracks learning milestones and achievements
"""

from typing import Dict, Any, List, Optional
from datetime import datetime

from .base_agent import BaseAgent, AgentResponse, HandoffSuggestion, HandoffConfidence
from ..config.user_profiles import UserProfile
from ..config.framework_configs import SupportedFrameworks
from ..config.constellation_configs import AgentRole


class AssessmentAgent(BaseAgent):
    """
    Agent specialized in evaluating learning progress and providing assessments.
    
    Specializes in:
    - Knowledge assessment and testing
    - Skill evaluation
    - Learning outcome measurement
    - Certification preparation
    """
    
    def __init__(self, llm_client: Any, **kwargs):
        system_prompt = """You are an assessment specialist for Python AI framework learning.
Your role is to evaluate learning progress and provide comprehensive assessments.

Key responsibilities:
- Create appropriate assessments and quizzes
- Evaluate user knowledge and skills
- Provide detailed feedback on performance
- Identify knowledge gaps and areas for improvement
- Suggest remediation strategies

Communication style:
- Be objective and constructive
- Provide specific, actionable feedback
- Explain assessment criteria clearly
- Offer encouragement alongside evaluation
- Focus on learning improvement opportunities"""

        super().__init__(
            agent_role=AgentRole.ASSESSMENT_AGENT,
            llm_client=llm_client,
            system_prompt=system_prompt,
            handoff_keywords=[
                "practice", "exercise", "learn more", "study", "review",
                "motivation", "encourage", "support"
            ],
            **kwargs
        )
    
    async def generate_response(
        self,
        user_message: str,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
        session_context: Dict[str, Any],
    ) -> AgentResponse:
        """Generate assessment response."""
        start_time = datetime.utcnow()
        
        # Enhanced system prompt with assessment context
        enhanced_prompt = self._build_assessment_prompt(user_profile, framework, module_id)
        
        messages = [
            {"role": "system", "content": enhanced_prompt},
            {"role": "user", "content": user_message}
        ]
        
        content = await self._call_llm(messages)
        
        self.add_to_history("user", user_message)
        self.add_to_history("assistant", content)
        
        handoff_suggestion = self.analyze_handoff_opportunity(user_message, session_context)
        processing_time = (datetime.utcnow() - start_time).total_seconds()
        
        return AgentResponse(
            agent_role=self.agent_role,
            content=content,
            response_type="assessment",
            handoff_suggestion=handoff_suggestion,
            learning_objectives_addressed=["knowledge_evaluation", "skill_assessment", "progress_measurement"],
            difficulty_level=self._determine_assessment_level(user_profile),
            estimated_reading_time_minutes=len(content.split()) / 180,
            includes_exercise="question" in content.lower() or "quiz" in content.lower(),
            processing_time_seconds=processing_time,
        )
    
    def analyze_handoff_opportunity(
        self,
        user_message: str,
        current_context: Dict[str, Any],
    ) -> Optional[HandoffSuggestion]:
        """Analyze handoff opportunities from assessment context."""
        message_lower = user_message.lower()
        
        # Check for learning needs after assessment
        if any(keyword in message_lower for keyword in ["learn more", "study", "review", "practice"]):
            return HandoffSuggestion(
                target_agent=AgentRole.PRACTICE_FACILITATOR,
                confidence=HandoffConfidence.HIGH,
                reason="User needs additional practice based on assessment results",
                context_data={"assessment_completed": True, "needs_practice": True}
            )
        
        # Check for motivational needs
        if any(keyword in message_lower for keyword in ["difficult", "hard", "struggling", "frustrated"]):
            return HandoffSuggestion(
                target_agent=AgentRole.MOTIVATIONAL_COACH,
                confidence=HandoffConfidence.HIGH,
                reason="User needs motivational support after assessment",
                context_data={"assessment_challenges": True}
            )
        
        return None
    
    def _build_assessment_prompt(
        self,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
    ) -> str:
        """Build enhanced assessment prompt with user context."""
        base_prompt = self.system_prompt
        
        context_additions = f"""

Assessment Context:
- User Skill Level: {user_profile.python_skill_level}
- Framework: {framework}
- Current Module: {module_id}
- Learning Hours: {user_profile.total_learning_hours}
- Completed Modules: {len(user_profile.completed_modules)}
- Learning Style: {user_profile.preferred_learning_style}

Create assessments appropriate for this skill level and learning context.
Provide constructive feedback that helps the user improve their understanding.
"""
        
        return base_prompt + context_additions
    
    def _determine_assessment_level(self, user_profile: UserProfile) -> str:
        """Determine appropriate assessment difficulty level."""
        skill_level = user_profile.python_skill_level.value
        
        if skill_level in ["none", "beginner"]:
            return "beginner"
        elif skill_level == "intermediate":
            return "intermediate"
        else:
            return "advanced"


class ProgressTrackerAgent(BaseAgent):
    """
    Agent specialized in tracking learning milestones and achievements.
    
    Specializes in:
    - Progress monitoring and reporting
    - Milestone tracking
    - Achievement recognition
    - Learning analytics
    """
    
    def __init__(self, llm_client: Any, **kwargs):
        system_prompt = """You are a progress tracker for Python AI framework learning.
Your role is to monitor, track, and report on learning progress and achievements.

Key responsibilities:
- Track learning milestones and achievements
- Provide progress reports and analytics
- Identify learning patterns and trends
- Celebrate accomplishments
- Suggest next learning steps

Communication style:
- Be data-driven and specific
- Highlight achievements and progress
- Use visual representations when helpful
- Provide actionable insights
- Maintain an encouraging tone"""

        super().__init__(
            agent_role=AgentRole.PROGRESS_TRACKER,
            llm_client=llm_client,
            system_prompt=system_prompt,
            handoff_keywords=[
                "next", "continue", "learn", "practice", "goal",
                "motivation", "encourage", "celebrate"
            ],
            **kwargs
        )
    
    async def generate_response(
        self,
        user_message: str,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
        module_id: str,
        session_context: Dict[str, Any],
    ) -> AgentResponse:
        """Generate progress tracking response."""
        start_time = datetime.utcnow()
        
        # Enhanced system prompt with progress context
        enhanced_prompt = self._build_progress_prompt(user_profile, framework)
        
        messages = [
            {"role": "system", "content": enhanced_prompt},
            {"role": "user", "content": user_message}
        ]
        
        content = await self._call_llm(messages)
        
        self.add_to_history("user", user_message)
        self.add_to_history("assistant", content)
        
        handoff_suggestion = self.analyze_handoff_opportunity(user_message, session_context)
        processing_time = (datetime.utcnow() - start_time).total_seconds()
        
        return AgentResponse(
            agent_role=self.agent_role,
            content=content,
            response_type="progress_tracking",
            handoff_suggestion=handoff_suggestion,
            learning_objectives_addressed=["progress_awareness", "goal_tracking", "achievement_recognition"],
            difficulty_level="informational",
            estimated_reading_time_minutes=len(content.split()) / 200,
            processing_time_seconds=processing_time,
        )
    
    def analyze_handoff_opportunity(
        self,
        user_message: str,
        current_context: Dict[str, Any],
    ) -> Optional[HandoffSuggestion]:
        """Analyze handoff opportunities from progress tracking context."""
        message_lower = user_message.lower()
        
        # Check for next learning steps
        if any(keyword in message_lower for keyword in ["next", "continue", "what should i"]):
            return HandoffSuggestion(
                target_agent=AgentRole.MENTOR,
                confidence=HandoffConfidence.MEDIUM,
                reason="User needs guidance on next learning steps after progress review",
                context_data={"progress_reviewed": True}
            )
        
        # Check for practice requests
        if any(keyword in message_lower for keyword in ["practice", "exercise", "work on"]):
            return HandoffSuggestion(
                target_agent=AgentRole.PRACTICE_FACILITATOR,
                confidence=HandoffConfidence.HIGH,
                reason="User wants to practice based on progress insights",
                context_data={"progress_context": True}
            )
        
        # Check for motivational needs
        if any(keyword in message_lower for keyword in ["celebrate", "achievement", "proud"]):
            return HandoffSuggestion(
                target_agent=AgentRole.MOTIVATIONAL_COACH,
                confidence=HandoffConfidence.MEDIUM,
                reason="User wants to celebrate achievements and get motivated",
                context_data={"achievements_highlighted": True}
            )
        
        return None
    
    def _build_progress_prompt(
        self,
        user_profile: UserProfile,
        framework: SupportedFrameworks,
    ) -> str:
        """Build enhanced progress prompt with detailed user data."""
        base_prompt = self.system_prompt
        
        # Calculate progress metrics
        completion_rate = len(user_profile.completed_modules) / 10 * 100  # Assuming ~10 total modules
        learning_velocity = user_profile.total_learning_hours / max(1, (datetime.utcnow() - user_profile.created_at).days)
        
        context_additions = f"""

Detailed Progress Data:
- Total Learning Time: {user_profile.total_learning_hours:.1f} hours
- Completed Modules: {len(user_profile.completed_modules)}
- Current Streak: {user_profile.current_streak_days} days
- Longest Streak: {user_profile.longest_streak_days} days
- Completion Rate: {completion_rate:.1f}%
- Learning Velocity: {learning_velocity:.2f} hours/day
- Account Age: {(datetime.utcnow() - user_profile.created_at).days} days
- Framework Focus: {framework}
- Learning Goals: {', '.join(user_profile.learning_goals) if user_profile.learning_goals else 'General learning'}

Provide specific, data-driven insights about their learning journey.
Highlight achievements and suggest areas for continued growth.
"""
        
        return base_prompt + context_additions
